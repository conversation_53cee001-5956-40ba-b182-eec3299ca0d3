<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信绑定管理 - 前端对接示例</title>
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <style>
        .loading {
            text-align: center;
            padding: 20px;
        }
        .error {
            color: #dc3545;
            padding: 10px;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            padding: 10px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            margin: 10px 0;
        }
        .wechat-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
    </style>
</head>
<body>
<div class="container-fluid mt-4">
    <h3><i class="fab fa-weixin mr-2"></i>微信绑定管理 - 前端对接示例</h3>
    
    <!-- 统计信息 -->
    <div class="row mb-4" id="statsContainer">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4 id="activeBindings">-</h4>
                    <p>活跃绑定</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4 id="totalWechatUsers">-</h4>
                    <p>微信用户</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4 id="activeTokens">-</h4>
                    <p>在线Token</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 搜索和操作区域 -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-link mr-2"></i>绑定关系列表</h5>
        </div>
        <div class="card-body">
            <!-- 搜索框 -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <span class="text-muted">共找到 <strong id="totalItems">0</strong> 个绑定关系</span>
                </div>
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchInput" placeholder="搜索用户名、真实姓名或微信昵称">
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchBindings()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 消息显示区域 -->
            <div id="messageContainer"></div>
            
            <!-- 加载状态 -->
            <div id="loadingContainer" class="loading" style="display: none;">
                <i class="fas fa-spinner fa-spin"></i> 加载中...
            </div>
            
            <!-- 数据表格 -->
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr class="text-center">
                            <th>序号</th>
                            <th>系统用户</th>
                            <th>微信信息</th>
                            <th>绑定类型</th>
                            <th>绑定时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="bindingsTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <nav id="paginationContainer" style="display: none;">
                <ul class="pagination" id="paginationList">
                    <!-- 分页将通过JavaScript动态生成 -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 解绑确认模态框 -->
<div class="modal fade" id="unbindModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="fas fa-unlink mr-2"></i>确认解绑</h5>
                <button type="button" class="close text-white" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <p>确定要解绑以下账号的微信绑定关系吗？</p>
                <div class="alert alert-warning">
                    <strong>用户名：</strong><span id="unbindUsername"></span><br>
                    <strong>真实姓名：</strong><span id="unbindRealName"></span><br>
                    <strong>微信昵称：</strong><span id="unbindWechatName"></span>
                </div>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    解绑后，该微信账号将无法登录系统，需要重新绑定才能使用。
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmUnbind()">
                    <i class="fas fa-unlink mr-1"></i>确认解绑
                </button>
            </div>
        </div>
    </div>
</div>

<script src="../assets/js/jquery.min.js"></script>
<script src="../assets/js/bootstrap.bundle.min.js"></script>
<script>
// 全局变量
let currentPage = 1;
let currentSearch = '';
let currentBindingId = null;

// 页面加载完成后初始化
$(document).ready(function() {
    loadBindings();
    
    // 搜索框回车事件
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) {
            searchBindings();
        }
    });
});

// 显示消息
function showMessage(message, type = 'info') {
    const alertClass = type === 'error' ? 'error' : (type === 'success' ? 'success' : 'alert alert-info');
    $('#messageContainer').html(`<div class="${alertClass}">${message}</div>`);
    
    // 3秒后自动隐藏
    setTimeout(() => {
        $('#messageContainer').empty();
    }, 3000);
}

// 显示加载状态
function showLoading(show = true) {
    if (show) {
        $('#loadingContainer').show();
        $('#bindingsTableBody').empty();
    } else {
        $('#loadingContainer').hide();
    }
}

// 加载绑定列表
function loadBindings(page = 1, search = '') {
    showLoading(true);
    currentPage = page;
    currentSearch = search;
    
    const params = new URLSearchParams({
        action: 'list',
        page: page
    });
    
    if (search) {
        params.append('search', search);
    }
    
    fetch(`../api/wechat_api.php?${params.toString().replace('action=list', 'action=bindings_list')}`)
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            
            if (data.success) {
                renderBindings(data.data.bindings);
                renderPagination(data.data.pagination);
                renderStats(data.data.stats);
                $('#totalItems').text(data.data.pagination.total_items);
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            showLoading(false);
            showMessage('加载数据失败: ' + error.message, 'error');
        });
}

// 渲染绑定列表
function renderBindings(bindings) {
    const tbody = $('#bindingsTableBody');
    tbody.empty();
    
    if (bindings.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="7" class="text-center py-4">
                    <i class="fas fa-info-circle mr-1 text-info"></i> 没有找到符合条件的绑定关系
                </td>
            </tr>
        `);
        return;
    }
    
    bindings.forEach((binding, index) => {
        const startNumber = (currentPage - 1) * 20 + 1;
        const avatarHtml = binding.avatar_url ? 
            `<img src="${binding.avatar_url}" class="wechat-avatar mr-2" alt="头像">` :
            `<div class="wechat-avatar mr-2 bg-secondary d-flex align-items-center justify-content-center">
                <i class="fab fa-weixin text-white"></i>
            </div>`;
        
        const actionHtml = binding.is_active ?
            `<button class="btn btn-sm btn-danger" onclick="showUnbindModal(${binding.binding_id}, '${binding.username}', '${binding.real_name || binding.username}', '${binding.nickname || '未设置昵称'}')">
                <i class="fas fa-unlink mr-1"></i>解绑
            </button>` :
            `<span class="text-muted">已解绑</span>`;
        
        tbody.append(`
            <tr>
                <td class="text-center">${startNumber + index}</td>
                <td>
                    <div>
                        <i class="fas fa-user-circle mr-2"></i>
                        <strong>${binding.username}</strong>
                    </div>
                    <small class="text-muted">
                        ${binding.real_name && binding.real_name !== binding.username ? '真实姓名: ' + binding.real_name + '<br>' : ''}
                        ${binding.role_text}
                    </small>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        ${avatarHtml}
                        <div>
                            <div><strong>${binding.nickname || '未设置昵称'}</strong></div>
                            <small class="text-muted">${binding.city || ''} ${binding.province || ''}</small>
                        </div>
                    </div>
                </td>
                <td class="text-center">
                    <span class="badge ${binding.bind_type === 'manual' ? 'badge-primary' : 'badge-info'}">
                        ${binding.bind_type_text}
                    </span>
                </td>
                <td class="text-center">
                    <div>${new Date(binding.bind_time).toLocaleDateString()}</div>
                    <small class="text-muted">${new Date(binding.bind_time).toLocaleTimeString()}</small>
                </td>
                <td class="text-center">
                    <span class="${binding.is_active ? 'text-success' : 'text-danger'}">
                        <i class="fas ${binding.is_active ? 'fa-check-circle' : 'fa-times-circle'} mr-1"></i>
                        ${binding.status_text}
                    </span>
                </td>
                <td class="text-center">${actionHtml}</td>
            </tr>
        `);
    });
}

// 渲染分页
function renderPagination(pagination) {
    if (pagination.total_pages <= 1) {
        $('#paginationContainer').hide();
        return;
    }
    
    $('#paginationContainer').show();
    const paginationList = $('#paginationList');
    paginationList.empty();
    
    // 首页和上一页
    if (pagination.current_page > 1) {
        paginationList.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadBindings(1, '${currentSearch}')">首页</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadBindings(${pagination.current_page - 1}, '${currentSearch}')">上一页</a>
            </li>
        `);
    }
    
    // 页码
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === pagination.current_page ? 'active' : '';
        paginationList.append(`
            <li class="page-item ${activeClass}">
                <a class="page-link" href="#" onclick="loadBindings(${i}, '${currentSearch}')">${i}</a>
            </li>
        `);
    }
    
    // 下一页和末页
    if (pagination.current_page < pagination.total_pages) {
        paginationList.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadBindings(${pagination.current_page + 1}, '${currentSearch}')">下一页</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadBindings(${pagination.total_pages}, '${currentSearch}')">末页</a>
            </li>
        `);
    }
}

// 渲染统计信息
function renderStats(stats) {
    $('#activeBindings').text(stats.active_bindings);
    $('#totalWechatUsers').text(stats.total_wechat_users);
    $('#activeTokens').text(stats.active_tokens);
}

// 搜索绑定
function searchBindings() {
    const search = $('#searchInput').val().trim();
    loadBindings(1, search);
}

// 显示解绑模态框
function showUnbindModal(bindingId, username, realName, wechatName) {
    currentBindingId = bindingId;
    $('#unbindUsername').text(username);
    $('#unbindRealName').text(realName);
    $('#unbindWechatName').text(wechatName);
    $('#unbindModal').modal('show');
}

// 确认解绑
function confirmUnbind() {
    if (!currentBindingId) return;
    
    fetch('../api/wechat_api.php?action=bindings_unbind', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            binding_id: currentBindingId
        })
    })
    .then(response => response.json())
    .then(data => {
        $('#unbindModal').modal('hide');
        
        if (data.success) {
            showMessage(data.message, 'success');
            loadBindings(currentPage, currentSearch); // 重新加载当前页
        } else {
            showMessage(data.message, 'error');
        }
    })
    .catch(error => {
        $('#unbindModal').modal('hide');
        showMessage('解绑操作失败: ' + error.message, 'error');
    });
}
</script>
</body>
</html>
