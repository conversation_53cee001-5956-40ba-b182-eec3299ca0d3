<?php
/**
 * 微信功能统一API入口文件
 * 整合微信登录和微信绑定管理功能
 */

// 定义API访问常量
define('API_ACCESS', true);

// 启动会话
session_start();

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 获取请求参数
$action = $_GET['action'] ?? $_POST['action'] ?? '';
$input = json_decode(file_get_contents('php://input'), true) ?: [];

// 路由分发
try {
    switch ($action) {
        // 微信登录相关功能
        case 'test_connection':
        case 'check_database':
        case 'init_database':
        case 'wechat_login':
        case 'bind_account':
        case 'verify_token':
        case 'logout':
        case 'clear_user_tokens':
        case 'change_password':
        case 'change_username':
        case 'unbind_account':
            // 调用微信登录API
            require_once 'wechat_login_api.php';
            break;
            
        // 微信绑定管理功能（需要管理员权限）
        case 'bindings_list':
        case 'bindings_unbind':
            // 验证管理员权限
            if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
                http_response_code(403);
                echo json_encode([
                    'success' => false,
                    'message' => '权限不足，只有管理员可以访问'
                ], JSON_UNESCAPED_UNICODE);
                exit();
            }
            
            // 重新映射action名称
            if ($action === 'bindings_list') {
                $_GET['action'] = 'list';
            } elseif ($action === 'bindings_unbind') {
                $_GET['action'] = 'unbind';
            }
            
            // 调用微信绑定管理API
            require_once 'wechat_bindings_api.php';
            break;
            
        default:
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => '未找到请求的操作: ' . $action
            ], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器内部错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
