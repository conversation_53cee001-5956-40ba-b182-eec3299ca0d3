<?php
/**
 * 微信绑定管理API接口
 * 支持获取绑定列表、解绑操作等
 */

// 防止直接访问
if (!defined('API_ACCESS')) {
    http_response_code(403);
    exit('Direct access not allowed');
}

require_once '../includes/config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 验证管理员权限
function checkAdminPermission() {
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'message' => '权限不足，只有管理员可以访问'
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
}

// 获取绑定列表
function getBindingsList() {
    global $pdo;
    
    try {
        $page = max(1, intval($_GET['page'] ?? 1));
        $search = isset($_GET['search']) ? "%{$_GET['search']}%" : '%';
        $offset = ($page - 1) * ITEMS_PER_PAGE;
        
        // 查询绑定关系
        $sql = "SELECT SQL_CALC_FOUND_ROWS 
                    uwb.id as binding_id,
                    uwb.bind_type,
                    uwb.bind_time,
                    uwb.bind_ip,
                    uwb.is_active,
                    u.id as user_id,
                    u.username,
                    u.real_name,
                    u.role,
                    u.is_station_staff,
                    wu.id as wechat_user_id,
                    wu.openid,
                    wu.nickname,
                    wu.avatar_url,
                    wu.city,
                    wu.province,
                    wu.created_at as wechat_created_at
                FROM user_wechat_bindings uwb
                JOIN users u ON uwb.user_id = u.id
                JOIN wechat_users wu ON uwb.wechat_user_id = wu.id
                WHERE (u.username LIKE ? OR u.real_name LIKE ? OR wu.nickname LIKE ?)
                ORDER BY uwb.bind_time DESC
                LIMIT ? OFFSET ?";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$search, $search, $search, ITEMS_PER_PAGE, $offset]);
        $bindings = $stmt->fetchAll();
        $total = $pdo->query("SELECT FOUND_ROWS()")->fetchColumn();
        
        // 获取统计信息
        $stats = [];
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_wechat_bindings WHERE is_active = 1");
        $stats['active_bindings'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM wechat_users");
        $stats['total_wechat_users'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM wechat_tokens WHERE expires_at > NOW()");
        $stats['active_tokens'] = $stmt->fetchColumn();
        
        // 处理数据格式
        foreach ($bindings as &$binding) {
            $binding['display_name'] = $binding['real_name'] ?: $binding['username'];
            $binding['role_text'] = $binding['role'] === 'admin' ? '管理员' : 
                                   ($binding['role'] === 'manager' ? '普通管理员' : '普通用户');
            if ($binding['is_station_staff']) {
                $binding['role_text'] .= ' | 一站人员';
            }
            $binding['bind_type_text'] = $binding['bind_type'] === 'manual' ? '手动绑定' : '自动绑定';
            $binding['status_text'] = $binding['is_active'] ? '激活' : '禁用';
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'bindings' => $bindings,
                'pagination' => [
                    'current_page' => $page,
                    'total_items' => $total,
                    'items_per_page' => ITEMS_PER_PAGE,
                    'total_pages' => ceil($total / ITEMS_PER_PAGE)
                ],
                'stats' => $stats
            ]
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => '获取绑定列表失败: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}

// 解绑操作
function unbindWechat() {
    global $pdo;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $bindingId = $input['binding_id'] ?? '';
        
        if (empty($bindingId)) {
            throw new Exception('参数错误');
        }
        
        // 获取绑定信息
        $stmt = $pdo->prepare("
            SELECT uwb.*, u.username, u.real_name, wu.openid, wu.nickname
            FROM user_wechat_bindings uwb
            JOIN users u ON uwb.user_id = u.id
            JOIN wechat_users wu ON uwb.wechat_user_id = wu.id
            WHERE uwb.id = ?
        ");
        $stmt->execute([$bindingId]);
        $binding = $stmt->fetch();
        
        if (!$binding) {
            throw new Exception('绑定关系不存在');
        }
        
        // 禁用绑定关系
        $stmt = $pdo->prepare("
            UPDATE user_wechat_bindings 
            SET is_active = 0, updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$bindingId]);
        
        // 删除相关token
        $stmt = $pdo->prepare("
            DELETE FROM wechat_tokens 
            WHERE user_id = ? AND wechat_user_id = ?
        ");
        $stmt->execute([$binding['user_id'], $binding['wechat_user_id']]);
        
        // 记录日志
        $stmt = $pdo->prepare("
            INSERT INTO wechat_login_logs (openid, user_id, action, ip_address, user_agent, result)
            VALUES (?, ?, 'unbind', ?, ?, 'success')
        ");
        $stmt->execute([
            $binding['openid'],
            $binding['user_id'],
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        
        // 记录操作日志
        logAction('解绑微信账号', 'wechat_bindings', [
            'username' => $binding['username'],
            'real_name' => $binding['real_name'],
            'wechat_nickname' => $binding['nickname']
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => '微信账号解绑成功'
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}

// 路由处理
try {
    checkAdminPermission();
    
    $action = $_GET['action'] ?? '';
    
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            if ($action === 'list' || empty($action)) {
                getBindingsList();
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => '未找到请求的操作'
                ], JSON_UNESCAPED_UNICODE);
            }
            break;
            
        case 'POST':
            if ($action === 'unbind') {
                unbindWechat();
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => '未找到请求的操作'
                ], JSON_UNESCAPED_UNICODE);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'message' => '不支持的请求方法'
            ], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器内部错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
