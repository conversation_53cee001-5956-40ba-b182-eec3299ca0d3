<?php
/**
 * 微信小程序登录API接口 - 修复版本
 * 修复了file_get_contents HTTPS问题，使用cURL替代
 */

require_once '../includes/config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 微信小程序配置
define('WECHAT_APPID', defined('WECHAT_MINI_APPID') ? WECHAT_MINI_APPID : 'wx6aa87efc3d1e3605');
define('WECHAT_SECRET', defined('WECHAT_MINI_SECRET') ? WECHAT_MINI_SECRET : '1f2902b99bedad690f6d3c82e0ecf0d4');

/**
 * 使用cURL发送HTTP请求
 */
function curlRequest($url, $data = null, $method = 'GET') {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; WechatLogin/1.0)');
    
    if ($method === 'POST' && $data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ]);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        throw new Exception('cURL Error: ' . $error);
    }
    
    if ($httpCode !== 200) {
        throw new Exception('HTTP Error: ' . $httpCode);
    }
    
    return $response;
}

/**
 * 调用微信API获取用户信息
 */
function getWechatUserInfo($code) {
    try {
        // 1. 通过code获取session_key和openid
        $url = "https://api.weixin.qq.com/sns/jscode2session";
        $params = [
            'appid' => WECHAT_APPID,
            'secret' => WECHAT_SECRET,
            'js_code' => $code,
            'grant_type' => 'authorization_code'
        ];
        
        $requestUrl = $url . '?' . http_build_query($params);
        error_log("微信API请求URL: " . $requestUrl);
        
        $response = curlRequest($requestUrl);
        error_log("微信API响应: " . $response);
        
        $data = json_decode($response, true);
        
        if (isset($data['errcode']) && $data['errcode'] !== 0) {
            error_log("微信API错误: " . json_encode($data));
            return ['error' => $data['errmsg'] ?? '微信登录失败'];
        }
        
        return [
            'openid' => $data['openid'],
            'session_key' => $data['session_key'],
            'unionid' => $data['unionid'] ?? null
        ];
    } catch (Exception $e) {
        error_log("微信API调用异常: " . $e->getMessage());
        return ['error' => '网络请求失败: ' . $e->getMessage()];
    }
}

/**
 * 检查数据库表是否存在
 */
function checkDatabaseTables() {
    global $pdo;
    
    $requiredTables = [
        'wechat_users',
        'user_wechat_bindings', 
        'wechat_tokens',
        'wechat_login_logs',
        'users'
    ];
    
    $missingTables = [];
    
    foreach ($requiredTables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() === 0) {
                $missingTables[] = $table;
            }
        } catch (Exception $e) {
            $missingTables[] = $table;
        }
    }
    
    return $missingTables;
}

/**
 * 生成并保存登录Token
 */
function generateLoginToken($userId, $wechatUserId = null, $loginType = 'wechat') {
    global $pdo;

    try {
        // 清理该用户的过期token
        $stmt = $pdo->prepare("DELETE FROM wechat_tokens WHERE user_id = ? AND expires_at < NOW()");
        $stmt->execute([$userId]);

        // 生成新token
        $token = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', time() + 86400 * 7); // 7天有效期
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // 保存token到数据库
        $stmt = $pdo->prepare("
            INSERT INTO wechat_tokens (user_id, wechat_user_id, token, login_type, expires_at, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$userId, $wechatUserId, $token, $loginType, $expiresAt, $ipAddress, $userAgent]);

        return [
            'token' => $token,
            'expires_at' => $expiresAt
        ];
    } catch (Exception $e) {
        error_log("生成Token失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 验证Token有效性
 */
function verifyToken($token) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT wt.user_id, wt.wechat_user_id, wt.expires_at, u.username, u.role, u.is_station_staff, wu.openid, wu.nickname
            FROM wechat_tokens wt
            JOIN users u ON wt.user_id = u.id
            LEFT JOIN wechat_users wu ON wt.wechat_user_id = wu.id
            WHERE wt.token = ? AND wt.expires_at > NOW()
        ");
        $stmt->execute([$token]);
        $tokenData = $stmt->fetch();

        if ($tokenData) {
            // 更新最后使用时间
            $stmt = $pdo->prepare("UPDATE wechat_tokens SET last_used_at = NOW() WHERE token = ?");
            $stmt->execute([$token]);

            $result = [
                'valid' => true,
                'user' => [
                    'id' => $tokenData['user_id'],
                    'username' => $tokenData['username'],
                    'role' => $tokenData['role'],
                    'is_station_staff' => $tokenData['is_station_staff']
                ],
                'expires_at' => $tokenData['expires_at']
            ];

            // 如果有微信用户信息，添加到返回结果中
            if ($tokenData['openid']) {
                $result['wechat'] = [
                    'openid' => $tokenData['openid'],
                    'nickname' => $tokenData['nickname']
                ];
            }

            return $result;
        }

        return ['valid' => false, 'error' => 'Token无效或已过期'];
    } catch (Exception $e) {
        error_log("验证Token失败: " . $e->getMessage());
        return ['valid' => false, 'error' => '验证失败'];
    }
}

/**
 * 撤销Token
 */
function revokeToken($token) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("DELETE FROM wechat_tokens WHERE token = ?");
        $stmt->execute([$token]);
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        error_log("撤销Token失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 清理用户所有Token（用于退出登录）
 */
function clearUserTokens($userId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("DELETE FROM wechat_tokens WHERE user_id = ?");
        $stmt->execute([$userId]);
        return true;
    } catch (Exception $e) {
        error_log("清理用户Token失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 保存或更新微信用户信息
 */
function saveWechatUser($openid, $sessionKey, $unionid = null, $userInfo = null) {
    global $pdo;

    try {
        // 检查用户是否已存在
        $stmt = $pdo->prepare("SELECT id FROM wechat_users WHERE openid = ?");
        $stmt->execute([$openid]);
        $existingUser = $stmt->fetch();

        if ($existingUser) {
            // 更新现有用户
            $stmt = $pdo->prepare("
                UPDATE wechat_users
                SET session_key = ?, unionid = ?, updated_at = NOW()
                WHERE openid = ?
            ");
            $stmt->execute([$sessionKey, $unionid, $openid]);
            return $existingUser['id'];
        } else {
            // 创建新用户
            $stmt = $pdo->prepare("
                INSERT INTO wechat_users (openid, unionid, session_key, nickname, avatar_url, gender, city, province, country)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $openid,
                $unionid,
                $sessionKey,
                $userInfo['nickName'] ?? null,
                $userInfo['avatarUrl'] ?? null,
                $userInfo['gender'] ?? 0,
                $userInfo['city'] ?? null,
                $userInfo['province'] ?? null,
                $userInfo['country'] ?? null
            ]);
            return $pdo->lastInsertId();
        }
    } catch (Exception $e) {
        error_log("保存微信用户信息失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 创建缺失的数据库表
 */
function createMissingTables($missingTables) {
    global $pdo;
    
    $tableSchemas = [
        'wechat_users' => "
            CREATE TABLE `wechat_users` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `openid` varchar(100) NOT NULL,
                `unionid` varchar(100) DEFAULT NULL,
                `session_key` varchar(100) DEFAULT NULL,
                `nickname` varchar(100) DEFAULT NULL,
                `avatar_url` varchar(500) DEFAULT NULL,
                `gender` tinyint(1) DEFAULT 0,
                `city` varchar(50) DEFAULT NULL,
                `province` varchar(50) DEFAULT NULL,
                `country` varchar(50) DEFAULT NULL,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `openid` (`openid`),
                KEY `unionid` (`unionid`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ",
        'user_wechat_bindings' => "
            CREATE TABLE `user_wechat_bindings` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `wechat_user_id` int(11) NOT NULL,
                `bind_type` enum('manual','auto') DEFAULT 'manual',
                `bind_ip` varchar(45) DEFAULT NULL,
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `user_wechat_unique` (`user_id`, `wechat_user_id`),
                KEY `user_id` (`user_id`),
                KEY `wechat_user_id` (`wechat_user_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ",
        'wechat_tokens' => "
            CREATE TABLE `wechat_tokens` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `wechat_user_id` int(11) DEFAULT NULL,
                `token` varchar(100) NOT NULL,
                `login_type` enum('wechat','password') DEFAULT 'wechat',
                `expires_at` timestamp NOT NULL,
                `ip_address` varchar(45) DEFAULT NULL,
                `user_agent` text,
                `last_used_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `token` (`token`),
                KEY `user_id` (`user_id`),
                KEY `expires_at` (`expires_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ",
        'wechat_login_logs' => "
            CREATE TABLE `wechat_login_logs` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `openid` varchar(100) DEFAULT NULL,
                `user_id` int(11) DEFAULT NULL,
                `action` varchar(50) NOT NULL,
                `ip_address` varchar(45) DEFAULT NULL,
                `user_agent` text,
                `result` enum('success','failed') NOT NULL,
                `error_message` text,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `openid` (`openid`),
                KEY `user_id` (`user_id`),
                KEY `created_at` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        "
    ];
    
    $createdTables = [];
    $errors = [];
    
    foreach ($missingTables as $table) {
        if (isset($tableSchemas[$table])) {
            try {
                $pdo->exec($tableSchemas[$table]);
                $createdTables[] = $table;
                error_log("成功创建表: $table");
            } catch (Exception $e) {
                $errors[] = "$table: " . $e->getMessage();
                error_log("创建表失败 $table: " . $e->getMessage());
            }
        }
    }
    
    return [
        'created' => $createdTables,
        'errors' => $errors
    ];
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
$action = $input['action'] ?? '';

// 记录请求日志
error_log("收到请求: action=$action, data=" . json_encode($input));

try {
    switch ($action) {
        case 'test_connection':
            // 测试连接
            echo json_encode([
                'status' => 'success',
                'message' => 'API连接正常',
                'data' => [
                    'server_time' => date('Y-m-d H:i:s'),
                    'wechat_appid' => WECHAT_APPID,
                    'php_version' => PHP_VERSION
                ]
            ]);
            break;
            
        case 'check_database':
            // 检查数据库
            $missingTables = checkDatabaseTables();
            if (empty($missingTables)) {
                echo json_encode([
                    'status' => 'success',
                    'message' => '数据库表完整',
                    'data' => ['missing_tables' => []]
                ]);
            } else {
                echo json_encode([
                    'status' => 'warning',
                    'message' => '缺少数据库表',
                    'data' => ['missing_tables' => $missingTables]
                ]);
            }
            break;
            
        case 'init_database':
            // 初始化数据库
            $missingTables = checkDatabaseTables();
            if (empty($missingTables)) {
                echo json_encode([
                    'status' => 'success',
                    'message' => '数据库表已存在，无需初始化'
                ]);
            } else {
                $result = createMissingTables($missingTables);
                echo json_encode([
                    'status' => empty($result['errors']) ? 'success' : 'partial',
                    'message' => empty($result['errors']) ? '数据库初始化成功' : '部分表创建失败',
                    'data' => $result
                ]);
            }
            break;
            
        case 'wechat_login':
            // 微信登录
            $code = $input['code'] ?? '';
            $userInfo = $input['userInfo'] ?? null;
            
            if (empty($code)) {
                throw new Exception('缺少微信授权码');
            }
            
            // 检查数据库表
            $missingTables = checkDatabaseTables();
            if (!empty($missingTables)) {
                throw new Exception('数据库表不完整，缺少: ' . implode(', ', $missingTables));
            }
            
            // 获取微信用户信息
            $wechatData = getWechatUserInfo($code);
            if (isset($wechatData['error'])) {
                throw new Exception($wechatData['error']);
            }

            $openid = $wechatData['openid'];
            $sessionKey = $wechatData['session_key'];
            $unionid = $wechatData['unionid'] ?? null;

            // 保存或更新微信用户信息
            $wechatUserId = saveWechatUser($openid, $sessionKey, $unionid, $userInfo);
            if (!$wechatUserId) {
                throw new Exception('保存微信用户信息失败');
            }

            // 检查是否已绑定系统账号
            $stmt = $pdo->prepare("
                SELECT uwb.user_id, u.username, u.role, u.is_station_staff
                FROM user_wechat_bindings uwb
                JOIN users u ON uwb.user_id = u.id
                WHERE uwb.wechat_user_id = ? AND uwb.is_active = 1
            ");
            $stmt->execute([$wechatUserId]);
            $binding = $stmt->fetch();

            if ($binding) {
                // 已绑定，直接登录成功
                // 生成登录token
                $tokenData = generateLoginToken($binding['user_id'], $wechatUserId, 'wechat');
                if (!$tokenData) {
                    throw new Exception('生成登录Token失败');
                }

                echo json_encode([
                    'status' => 'success',
                    'message' => '登录成功',
                    'data' => [
                        'token' => $tokenData['token'],
                        'expires_at' => $tokenData['expires_at'],
                        'user' => [
                            'id' => $binding['user_id'],
                            'username' => $binding['username'],
                            'role' => $binding['role'],
                            'is_station_staff' => $binding['is_station_staff']
                        ],
                        'wechat' => [
                            'openid' => substr($openid, 0, 10) . '...',
                            'nickname' => $userInfo['nickName'] ?? ''
                        ]
                    ]
                ]);
            } else {
                // 未绑定，返回需要绑定的状态
                echo json_encode([
                    'status' => 'need_bind',
                    'message' => '微信账号尚未绑定系统账号，请先绑定',
                    'data' => [
                        'openid' => $openid,
                        'wechat_user_id' => $wechatUserId,
                        'nickname' => $userInfo['nickName'] ?? ''
                    ]
                ]);
            }
            break;

        case 'bind_account':
            // 绑定系统账号
            $openid = $input['openid'] ?? '';
            $wechatUserId = $input['wechat_user_id'] ?? '';
            $username = $input['username'] ?? '';
            $password = $input['password'] ?? '';

            if (empty($openid) || empty($username) || empty($password)) {
                throw new Exception('参数不完整');
            }

            // 验证系统账号
            $stmt = $pdo->prepare("SELECT id, username, role, password, is_station_staff FROM users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch();

            if (!$user || !password_verify($password, $user['password'])) {
                throw new Exception('用户名或密码错误');
            }

            // 检查是否已经存在绑定关系（包括禁用的）
            $stmt = $pdo->prepare("
                SELECT id, is_active
                FROM user_wechat_bindings
                WHERE user_id = ? AND wechat_user_id = ?
            ");
            $stmt->execute([$user['id'], $wechatUserId]);
            $existingBinding = $stmt->fetch();

            if ($existingBinding) {
                if ($existingBinding['is_active']) {
                    throw new Exception('该账号已绑定此微信，无需重复绑定');
                } else {
                    // 重新激活已存在的绑定关系
                    $stmt = $pdo->prepare("
                        UPDATE user_wechat_bindings
                        SET is_active = 1, bind_ip = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$_SERVER['REMOTE_ADDR'] ?? '', $existingBinding['id']]);
                }
            } else {
                // 检查是否已经绑定其他微信账号
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as count
                    FROM user_wechat_bindings
                    WHERE user_id = ? AND is_active = 1
                ");
                $stmt->execute([$user['id']]);
                $bindCount = $stmt->fetchColumn();

                if ($bindCount > 0) {
                    throw new Exception('该系统账号已绑定其他微信账号');
                }

                // 创建新的绑定关系
                $stmt = $pdo->prepare("
                    INSERT INTO user_wechat_bindings (user_id, wechat_user_id, bind_type, bind_ip)
                    VALUES (?, ?, 'manual', ?)
                ");
                $stmt->execute([$user['id'], $wechatUserId, $_SERVER['REMOTE_ADDR'] ?? '']);
            }

            // 生成登录token
            $tokenData = generateLoginToken($user['id'], $wechatUserId, 'wechat');
            if (!$tokenData) {
                throw new Exception('生成登录Token失败');
            }

            echo json_encode([
                'status' => 'success',
                'message' => '绑定成功',
                'data' => [
                    'token' => $tokenData['token'],
                    'expires_at' => $tokenData['expires_at'],
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'role' => $user['role'],
                        'is_station_staff' => $user['is_station_staff']
                    ]
                ]
            ]);
            break;

        case 'verify_token':
            // 验证Token
            $token = $input['token'] ?? '';

            if (empty($token)) {
                throw new Exception('缺少Token');
            }

            $result = verifyToken($token);

            if ($result['valid']) {
                $responseData = [
                    'user' => $result['user'],
                    'expires_at' => $result['expires_at']
                ];

                // 如果有微信信息，添加到响应中
                if (isset($result['wechat'])) {
                    $responseData['wechat'] = $result['wechat'];
                }

                echo json_encode([
                    'status' => 'success',
                    'message' => 'Token有效',
                    'data' => $responseData
                ]);
            } else {
                echo json_encode([
                    'status' => 'error',
                    'message' => $result['error']
                ]);
            }
            break;

        case 'logout':
            // 退出登录
            $token = $input['token'] ?? '';

            if (empty($token)) {
                throw new Exception('缺少Token');
            }

            $success = revokeToken($token);

            echo json_encode([
                'status' => $success ? 'success' : 'error',
                'message' => $success ? '退出登录成功' : '退出登录失败'
            ]);
            break;

        case 'clear_user_tokens':
            // 清理用户所有Token
            $userId = $input['user_id'] ?? '';

            if (empty($userId)) {
                throw new Exception('缺少用户ID');
            }

            $success = clearUserTokens($userId);

            echo json_encode([
                'status' => $success ? 'success' : 'error',
                'message' => $success ? '清理Token成功' : '清理Token失败'
            ]);
            break;

        case 'change_password':
            // 修改密码
            $token = $input['token'] ?? '';
            $oldPassword = $input['old_password'] ?? '';
            $newPassword = $input['new_password'] ?? '';

            if (empty($token) || empty($oldPassword) || empty($newPassword)) {
                throw new Exception('参数不完整');
            }

            // 验证Token
            $tokenResult = verifyToken($token);
            if (!$tokenResult['valid']) {
                throw new Exception('Token无效');
            }

            $userId = $tokenResult['user']['id'];

            // 验证旧密码
            $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();

            if (!$user || !password_verify($oldPassword, $user['password'])) {
                throw new Exception('当前密码错误');
            }

            // 更新密码
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            $stmt->execute([$hashedPassword, $userId]);

            echo json_encode([
                'status' => 'success',
                'message' => '密码修改成功'
            ]);
            break;

        case 'change_username':
            // 修改用户名
            $token = $input['token'] ?? '';
            $newUsername = $input['new_username'] ?? '';

            if (empty($token) || empty($newUsername)) {
                throw new Exception('参数不完整');
            }

            // 验证Token
            $tokenResult = verifyToken($token);
            if (!$tokenResult['valid']) {
                throw new Exception('Token无效');
            }

            $userId = $tokenResult['user']['id'];

            // 检查用户名是否已存在
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
            $stmt->execute([$newUsername, $userId]);
            if ($stmt->fetch()) {
                throw new Exception('用户名已存在');
            }

            // 更新用户名
            $stmt = $pdo->prepare("UPDATE users SET username = ? WHERE id = ?");
            $stmt->execute([$newUsername, $userId]);

            echo json_encode([
                'status' => 'success',
                'message' => '用户名修改成功'
            ]);
            break;

        case 'unbind_account':
            // 解除微信绑定
            $token = $input['token'] ?? '';

            if (empty($token)) {
                throw new Exception('缺少Token');
            }

            // 验证Token
            $tokenResult = verifyToken($token);
            if (!$tokenResult['valid']) {
                throw new Exception('Token无效');
            }

            $userId = $tokenResult['user']['id'];

            // 删除绑定关系
            $stmt = $pdo->prepare("DELETE FROM user_wechat_bindings WHERE user_id = ?");
            $stmt->execute([$userId]);

            // 清除用户所有Token
            clearUserTokens($userId);

            echo json_encode([
                'status' => 'success',
                'message' => '解绑成功'
            ]);
            break;

        default:
            throw new Exception('无效的操作');
    }
    
} catch (Exception $e) {
    error_log("API错误: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>
