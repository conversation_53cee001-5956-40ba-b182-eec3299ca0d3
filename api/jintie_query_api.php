<?php
/**
 * 一站津贴查询专用API
 * 提供严格的权限控制，只允许一站人员和管理员查看津贴数据
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once '../includes/config.php';

// 获取输入数据
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    $input = $_POST;
}

// 验证action参数
if (!isset($input['action'])) {
    echo json_encode(['status' => 'error', 'message' => '缺少action参数']);
    exit;
}

// 创建必要的表结构
createRequiredTables();

// 根据action执行相应操作
switch ($input['action']) {
    case 'station_login':
        stationLogin($input);
        break;
    case 'get_jintie_data':
        getJintieData($input);
        break;
    case 'get_latest_jintie_data':
        getLatestJintieData($input);
        break;
    case 'get_jintie_history':
        getJintieHistory($input);
        break;
    case 'check_station_auth':
        checkStationAuth($input);
        break;
    case 'change_password':
        changePassword($input);
        break;
    case 'get_user_list':
        getUserList($input);
        break;
    case 'create_user':
        createUser($input);
        break;
    case 'update_user':
        updateUser($input);
        break;
    case 'delete_user':
        deleteUser($input);
        break;
    case 'reset_user_password':
        resetUserPassword($input);
        break;
    case 'get_personal_yearly_data':
        getPersonalYearlyData($input);
        break;
    case 'logout':
        handleLogout($input);
        break;
    default:
        echo json_encode(['status' => 'error', 'message' => '无效的action参数']);
        break;
}

// 获取客户端IP地址
function getClientIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

// 创建必要的数据表
function createRequiredTables() {
    global $pdo;

    try {
        // 创建登录尝试表
        $pdo->exec("CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            attempt_time DATETIME NOT NULL,
            is_success TINYINT(1) DEFAULT 0,
            module VARCHAR(50) DEFAULT 'jintie_query',
            INDEX idx_username_time (username, attempt_time),
            INDEX idx_ip_time (ip_address, attempt_time)
        )");
    } catch (PDOException $e) {
        error_log("创建表失败: " . $e->getMessage());
    }
}

// 检查登录尝试次数，如果超过限制则锁定
function checkLoginAttempts($username) {
    global $pdo;

    $ip = getClientIP();
    $lockoutTime = 3600; // 1小时锁定时间（秒）
    $maxAttempts = 5; // 最大尝试次数

    try {
        // 检查IP锁定（只锁定IP，不锁定账号）
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM login_attempts
                              WHERE ip_address = ?
                              AND is_success = 0
                              AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $stmt->execute([$ip]);
        $ipAttempts = $stmt->fetchColumn();

        if ($ipAttempts >= $maxAttempts) {
            $remainingTime = getRemainingLockTime($ip, 'ip_address');
            return [
                'locked' => true,
                'message' => "IP已锁定，请{$remainingTime}分钟后再试"
            ];
        }

        return ['locked' => false];
    } catch (PDOException $e) {
        error_log("Login attempts check error: " . $e->getMessage());
        return ['locked' => false];
    }
}

// 记录登录尝试
function recordLoginAttempt($username, $isSuccess = false) {
    global $pdo;

    $ip = getClientIP();

    try {
        $stmt = $pdo->prepare("INSERT INTO login_attempts (username, ip_address, attempt_time, is_success, module)
                              VALUES (?, ?, NOW(), ?, 'jintie_query')");
        $stmt->execute([$username, $ip, $isSuccess ? 1 : 0]);
        return true;
    } catch (PDOException $e) {
        error_log("Record login attempt error: " . $e->getMessage());
        return false;
    }
}

// 重置登录尝试次数
function resetLoginAttempts($username) {
    global $pdo;

    $ip = getClientIP();

    try {
        // 只重置IP的失败记录
        $stmt = $pdo->prepare("UPDATE login_attempts
                                SET is_success = 1
                                WHERE ip_address = ? AND is_success = 0");
        $stmt->execute([$ip]);
        return true;
    } catch (PDOException $e) {
        error_log("Reset login attempts error: " . $e->getMessage());
        return false;
    }
}

// 获取剩余锁定时间（分钟）
function getRemainingLockTime($value, $type = 'ip_address') {
    global $pdo;

    try {
        $column = ($type == 'username') ? 'username' : 'ip_address';

        $stmt = $pdo->prepare("SELECT
                                TIMESTAMPDIFF(SECOND, NOW(), DATE_ADD(MIN(attempt_time), INTERVAL 1 HOUR)) as remaining_seconds
                              FROM login_attempts
                              WHERE {$column} = ?
                              AND is_success = 0
                              AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $stmt->execute([$value]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result && $result['remaining_seconds'] > 0) {
            return ceil(abs($result['remaining_seconds']) / 60);
        }

        return 60;
    } catch (PDOException $e) {
        error_log("Get remaining lock time error: " . $e->getMessage());
        return 60;
    }
}

/**
 * 一站人员专用登录
 */
function stationLogin($input) {
    global $pdo;
    
    try {
        // 参数验证
        if (!isset($input['username']) || !isset($input['password'])) {
            echo json_encode(['status' => 'error', 'message' => '用户名和密码不能为空']);
            exit;
        }
        
        $username = trim($input['username']);
        $password = trim($input['password']);
        
        if (empty($username) || empty($password)) {
            echo json_encode(['status' => 'error', 'message' => '用户名和密码不能为空']);
            exit;
        }

        // 检查是否被锁定
        $lockStatus = checkLoginAttempts($username);
        if ($lockStatus['locked']) {
            echo json_encode(['status' => 'error', 'message' => $lockStatus['message']]);
            recordLoginAttempt($username, false);
            exit;
        }

        // 查询用户信息
        $stmt = $pdo->prepare("SELECT id, username, role, password, is_station_staff FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();
        
        if (!$user) {
            recordLoginAttempt($username, false);
            echo json_encode(['status' => 'error', 'message' => '用户不存在']);
            exit;
        }

        // 验证密码
        if (!password_verify($password, $user['password']) && $password !== $user['password']) {
            recordLoginAttempt($username, false);
            echo json_encode(['status' => 'error', 'message' => '密码错误']);
            exit;
        }
        
        // 权限验证：只允许admin和拥有一站人员身份的用户登录
        if ($user['role'] !== 'admin' && $user['is_station_staff'] != 1) {
            recordLoginAttempt($username, false);
            echo json_encode(['status' => 'error', 'message' => '身份不正确，不允许登录。只有一站人员才能使用此功能。']);
            exit;
        }

        // 登录成功，重置失败尝试记录
        resetLoginAttempts($username);
        recordLoginAttempt($username, true);

        // 记录操作日志
        if (function_exists('logAction')) {
            // 临时设置SESSION以便logAction能获取到用户ID
            $_SESSION['user'] = ['id' => $user['id'], 'username' => $user['username']];
            logAction('用户登录', 'login', ['username' => $username]);
            // 清除临时SESSION（API接口不需要保持SESSION）
            unset($_SESSION['user']);
        }

        // 登录成功
        echo json_encode([
            'status' => 'success',
            'data' => [
                'token' => md5($username . time() . 'station'),
                'username' => $user['username'],
                'role' => $user['role'],
                'user_id' => $user['id'],
                'expire_time' => date('Y-m-d H:i:s', strtotime('+7 days'))
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '登录服务暂时不可用']);
    }
}

/**
 * 处理退出登录
 */
function handleLogout($input) {
    try {
        // 验证基本参数
        if (!isset($input['username'])) {
            echo json_encode(['status' => 'error', 'message' => '缺少用户名参数']);
            exit;
        }

        $username = $input['username'];

        // 查询用户信息以获取用户ID
        global $pdo;
        $stmt = $pdo->prepare("SELECT id, username FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();

        if ($user) {
            // 记录操作日志
            if (function_exists('logAction')) {
                // 临时设置SESSION以便logAction能获取到用户ID
                $_SESSION['user'] = ['id' => $user['id'], 'username' => $user['username']];
                logAction('用户退出', 'logout', ['username' => $username]);
                // 清除临时SESSION
                unset($_SESSION['user']);
            }
        }

        echo json_encode(['status' => 'success', 'message' => '退出成功']);

    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '退出处理失败']);
    }
}

/**
 * 获取津贴数据
 */
function getJintieData($input) {
    global $pdo;
    
    try {
        // 验证token
        if (!isset($input['token']) || !isset($input['username'])) {
            echo json_encode(['status' => 'error', 'message' => '未登录或token无效']);
            exit;
        }
        
        $username = $input['username'];
        $recordDate = $input['record_date'] ?? date('Y-m-d');
        
        // 获取用户角色
        $userStmt = $pdo->prepare("SELECT role, is_station_staff FROM users WHERE username = ?");
        $userStmt->execute([$username]);
        $userInfo = $userStmt->fetch();
        
        if (!$userInfo) {
            echo json_encode(['status' => 'error', 'message' => '用户不存在']);
            exit;
        }
        
        // 构建查询条件
        $whereClause = "WHERE record_date = ?";
        $params = [$recordDate];

        // 检查是否查看全部用户
        $viewAll = isset($input['view_all']) ? $input['view_all'] : false;

        // 权限控制：管理员可以查看全部，普通用户根据view_all参数决定
        if ($userInfo['role'] !== 'admin' && $userInfo['role'] !== 'manager') {
            // 普通用户：如果不是查看全部模式，只能查看自己的数据
            if (!$viewAll) {
                $whereClause .= " AND name = ?";
                $params[] = $username;
            }
        }
        
        // 查询津贴数据
        $sql = "SELECT 
                    id, record_date, month_year, name, dept,
                    fixed_amount, floating_amount, fixed_actual, floating_actual,
                    days, deduction, personal_bonus, general_bonus, total_amount,
                    is_fixed_bonus, floating_ratio, remark, created_at
                FROM jintie_records 
                {$whereClause}
                ORDER BY id";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 格式化数据
        foreach ($records as &$record) {
            $record['fixed_amount'] = floatval($record['fixed_amount']);
            $record['floating_amount'] = floatval($record['floating_amount']);
            $record['fixed_actual'] = floatval($record['fixed_actual']);
            $record['floating_actual'] = floatval($record['floating_actual']);
            $record['total_amount'] = floatval($record['total_amount']);
            $record['deduction'] = floatval($record['deduction']);
            $record['personal_bonus'] = floatval($record['personal_bonus']);
            $record['general_bonus'] = floatval($record['general_bonus']);
        }
        
        echo json_encode([
            'status' => 'success',
            'data' => [
                'records' => $records,
                'record_date' => $recordDate,
                'user_role' => $userInfo['role'],
                'total_count' => count($records)
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '获取数据失败']);
    }
}

/**
 * 获取津贴历史记录
 */
function getJintieHistory($input) {
    global $pdo;
    
    try {
        // 验证token
        if (!isset($input['token']) || !isset($input['username'])) {
            echo json_encode(['status' => 'error', 'message' => '未登录或token无效']);
            exit;
        }
        
        $username = $input['username'];
        $year = $input['year'] ?? date('Y');
        
        // 获取用户角色
        $userStmt = $pdo->prepare("SELECT role, is_station_staff FROM users WHERE username = ?");
        $userStmt->execute([$username]);
        $userInfo = $userStmt->fetch();
        
        if (!$userInfo) {
            echo json_encode(['status' => 'error', 'message' => '用户不存在']);
            exit;
        }
        
        // 构建查询条件
        $whereClause = "WHERE YEAR(record_date) = ?";
        $params = [$year];
        
        // 非管理员只能查看自己的数据
        if ($userInfo['role'] !== 'admin') {
            $whereClause .= " AND name = ?";
            $params[] = $username;
        }
        
        // 查询历史记录，按日期分组
        $sql = "SELECT 
                    record_date, month_year,
                    COUNT(*) as record_count,
                    SUM(total_amount) as total_amount
                FROM jintie_records 
                {$whereClause}
                GROUP BY record_date, month_year
                ORDER BY record_date DESC";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 格式化数据
        foreach ($records as &$record) {
            $record['total_amount'] = floatval($record['total_amount']);
            $record['record_count'] = intval($record['record_count']);
        }
        
        echo json_encode([
            'status' => 'success',
            'data' => [
                'records' => $records,
                'year' => $year,
                'user_role' => $userInfo['role'],
                'total_count' => count($records)
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '获取历史数据失败']);
    }
}

/**
 * 检查一站权限
 */
function checkStationAuth($input) {
    global $pdo;
    
    try {
        if (!isset($input['username'])) {
            echo json_encode(['status' => 'error', 'message' => '缺少用户名']);
            exit;
        }
        
        $username = $input['username'];
        
        // 获取用户信息
        $stmt = $pdo->prepare("SELECT role, is_station_staff FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();

        if (!$user) {
            echo json_encode(['status' => 'error', 'message' => '用户不存在']);
            exit;
        }

        // 检查权限：管理员或拥有一站人员身份
        $hasAuth = ($user['role'] === 'admin' || $user['is_station_staff'] == 1);
        
        echo json_encode([
            'status' => 'success',
            'data' => [
                'has_auth' => $hasAuth,
                'role' => $user['role'],
                'is_station_staff' => $user['is_station_staff'] == 1
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '权限检查失败']);
    }
}

/**
 * 修改密码
 */
function changePassword($input) {
    global $pdo;

    try {
        // 验证token和必要参数
        if (!isset($input['token']) || !isset($input['username']) ||
            !isset($input['old_password']) || !isset($input['new_password'])) {
            echo json_encode(['status' => 'error', 'message' => '参数不完整']);
            exit;
        }

        $username = trim($input['username']);
        $oldPassword = trim($input['old_password']);
        $newPassword = trim($input['new_password']);

        // 验证新密码长度
        if (strlen($newPassword) < 6) {
            echo json_encode(['status' => 'error', 'message' => '新密码长度不能少于6位']);
            exit;
        }

        // 查询用户信息
        $stmt = $pdo->prepare("SELECT id, username, password, role, is_station_staff FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();

        if (!$user) {
            echo json_encode(['status' => 'error', 'message' => '用户不存在']);
            exit;
        }

        // 验证权限：只允许admin和拥有一站人员身份的用户修改密码
        if ($user['role'] !== 'admin' && $user['is_station_staff'] != 1) {
            echo json_encode(['status' => 'error', 'message' => '权限不足']);
            exit;
        }

        // 验证当前密码
        if (!password_verify($oldPassword, $user['password']) && $oldPassword !== $user['password']) {
            echo json_encode(['status' => 'error', 'message' => '当前密码错误']);
            exit;
        }

        // 加密新密码
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

        // 更新密码
        $updateStmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
        $result = $updateStmt->execute([$hashedPassword, $user['id']]);

        if ($result) {
            // 记录操作日志
            if (function_exists('logAction')) {
                // 临时设置SESSION以便logAction能获取到用户ID
                $_SESSION['user'] = ['id' => $user['id'], 'username' => $user['username']];
                logAction('修改密码', 'users', ['username' => $username]);
                unset($_SESSION['user']);
            }

            echo json_encode([
                'status' => 'success',
                'message' => '密码修改成功'
            ]);
        } else {
            echo json_encode(['status' => 'error', 'message' => '密码修改失败']);
        }

    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '密码修改服务暂时不可用']);
    }
}

/**
 * 获取最新津贴数据
 */
function getLatestJintieData($input) {
    global $pdo;

    try {
        // 验证token
        if (!isset($input['token']) || !isset($input['username'])) {
            echo json_encode(['status' => 'error', 'message' => '未登录或token无效']);
            exit;
        }

        $username = $input['username'];

        // 获取用户角色
        $userStmt = $pdo->prepare("SELECT role, is_station_staff FROM users WHERE username = ?");
        $userStmt->execute([$username]);
        $userInfo = $userStmt->fetch();

        if (!$userInfo) {
            echo json_encode(['status' => 'error', 'message' => '用户不存在']);
            exit;
        }

        // 确定要查询的日期
        $targetDate = null;
        if (isset($input['date']) && !empty($input['date'])) {
            // 如果传入了指定日期，使用指定日期
            $targetDate = $input['date'];
        } else {
            // 否则获取最新的记录日期
            $latestDateSql = "SELECT record_date FROM jintie_records ORDER BY record_date DESC LIMIT 1";
            $latestDateStmt = $pdo->prepare($latestDateSql);
            $latestDateStmt->execute();
            $latestDateResult = $latestDateStmt->fetch();

            if (!$latestDateResult) {
                echo json_encode([
                    'status' => 'success',
                    'data' => [
                        'records' => [],
                        'record_date' => null,
                        'month_year' => null,
                        'user_role' => $userInfo['role'],
                        'total_count' => 0
                    ]
                ]);
                exit;
            }

            $targetDate = $latestDateResult['record_date'];
        }

        // 构建查询条件
        $whereClause = "WHERE record_date = ?";
        $params = [$targetDate];

        // 检查是否查看全部用户
        $viewAll = isset($input['view_all']) ? $input['view_all'] : false;

        // 权限控制：管理员可以查看全部，普通用户根据view_all参数决定
        if ($userInfo['role'] !== 'admin' && $userInfo['role'] !== 'manager') {
            // 普通用户：如果不是查看全部模式，只能查看自己的数据
            if (!$viewAll) {
                $whereClause .= " AND name = ?";
                $params[] = $username;
            }
        }

        // 查询津贴数据
        $sql = "SELECT
                    id, record_date, month_year, name, dept,
                    fixed_amount, floating_amount, fixed_actual, floating_actual,
                    days, deduction, personal_bonus, general_bonus, total_amount,
                    is_fixed_bonus, floating_ratio, remark, created_at
                FROM jintie_records
                {$whereClause}
                ORDER BY id";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 格式化数据
        foreach ($records as &$record) {
            $record['total_amount'] = floatval($record['total_amount']);
            $record['fixed_amount'] = floatval($record['fixed_amount']);
            $record['floating_amount'] = floatval($record['floating_amount']);
            $record['fixed_actual'] = floatval($record['fixed_actual']);
            $record['floating_actual'] = floatval($record['floating_actual']);
            $record['deduction'] = floatval($record['deduction']);
            $record['personal_bonus'] = floatval($record['personal_bonus']);
            $record['general_bonus'] = floatval($record['general_bonus']);
            $record['days'] = intval($record['days']);
        }

        // 获取月度信息
        $monthYear = !empty($records) ? $records[0]['month_year'] : null;

        echo json_encode([
            'status' => 'success',
            'data' => [
                'records' => $records,
                'record_date' => $targetDate,
                'month_year' => $monthYear,
                'user_role' => $userInfo['role'],
                'total_count' => count($records)
            ]
        ]);

    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '获取数据失败']);
    }
}

/**
 * 获取用户列表
 */
function getUserList($input) {
    global $pdo;

    try {
        // 验证token和权限
        if (!isset($input['token']) || !isset($input['username'])) {
            echo json_encode(['status' => 'error', 'message' => '未登录或token无效']);
            exit;
        }

        $username = $input['username'];

        // 获取当前用户角色
        $userStmt = $pdo->prepare("SELECT role FROM users WHERE username = ?");
        $userStmt->execute([$username]);
        $currentUser = $userStmt->fetch();

        if (!$currentUser || ($currentUser['role'] !== 'admin' && $currentUser['role'] !== 'manager')) {
            echo json_encode(['status' => 'error', 'message' => '权限不足']);
            exit;
        }

        // 查询所有用户（参考现有用户管理页面的实现）
        $sql = "SELECT id, username, role, COALESCE(is_station_staff, 0) as is_station_staff FROM users ORDER BY id DESC";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 格式化数据
        foreach ($users as &$user) {
            $user['is_station_staff'] = intval($user['is_station_staff']);
            // 确保字段存在
            if (!isset($user['is_station_staff'])) {
                $user['is_station_staff'] = 0;
            }
        }

        echo json_encode([
            'status' => 'success',
            'data' => [
                'users' => $users,
                'total_count' => count($users)
            ]
        ]);

    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '获取用户列表失败']);
    }
}

/**
 * 创建用户
 */
function createUser($input) {
    global $pdo;

    try {
        // 验证token和权限
        if (!isset($input['token']) || !isset($input['username'])) {
            echo json_encode(['status' => 'error', 'message' => '未登录或token无效']);
            exit;
        }

        $username = $input['username'];

        // 获取当前用户角色
        $userStmt = $pdo->prepare("SELECT role FROM users WHERE username = ?");
        $userStmt->execute([$username]);
        $currentUser = $userStmt->fetch();

        if (!$currentUser || ($currentUser['role'] !== 'admin' && $currentUser['role'] !== 'manager')) {
            echo json_encode(['status' => 'error', 'message' => '权限不足']);
            exit;
        }

        $userData = $input['user_data'];

        // 验证必填字段
        if (empty($userData['username']) || empty($userData['password'])) {
            echo json_encode(['status' => 'error', 'message' => '用户名和密码不能为空']);
            exit;
        }

        // 检查用户名是否已存在
        $checkStmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $checkStmt->execute([$userData['username']]);
        if ($checkStmt->fetch()) {
            echo json_encode(['status' => 'error', 'message' => '用户名已存在']);
            exit;
        }

        // 创建用户（参考现有用户管理页面的实现）
        $hashedPassword = password_hash($userData['password'], PASSWORD_DEFAULT);
        $insertStmt = $pdo->prepare("INSERT INTO users (username, role, password, is_station_staff) VALUES (?,?,?,?)");

        $result = $insertStmt->execute([
            $userData['username'],
            $userData['role'] ?? 'user',
            $hashedPassword,
            $userData['is_station_staff'] ?? 0
        ]);

        if ($result) {
            // 获取新创建的用户ID
            $newUserId = $pdo->lastInsertId();

            // 记录操作日志
            if (function_exists('logAction')) {
                // 获取当前操作用户的ID
                $operatorStmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
                $operatorStmt->execute([$username]);
                $operator = $operatorStmt->fetch();

                if ($operator) {
                    // 临时设置SESSION以便logAction能获取到用户ID
                    $_SESSION['user'] = ['id' => $operator['id'], 'username' => $username];
                    logAction('创建新用户', 'users', [
                        'username' => $userData['username'],
                        'role' => $userData['role'] ?? 'user',
                        'new_user_id' => $newUserId
                    ]);
                    unset($_SESSION['user']);
                }
            }

            echo json_encode(['status' => 'success', 'message' => '用户创建成功']);
        } else {
            echo json_encode(['status' => 'error', 'message' => '用户创建失败']);
        }

    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '创建用户失败']);
    }
}

/**
 * 更新用户
 */
function updateUser($input) {
    global $pdo;

    try {
        // 验证token和权限
        if (!isset($input['token']) || !isset($input['username'])) {
            echo json_encode(['status' => 'error', 'message' => '未登录或token无效']);
            exit;
        }

        $username = $input['username'];

        // 获取当前用户角色
        $userStmt = $pdo->prepare("SELECT role FROM users WHERE username = ?");
        $userStmt->execute([$username]);
        $currentUser = $userStmt->fetch();

        if (!$currentUser || ($currentUser['role'] !== 'admin' && $currentUser['role'] !== 'manager')) {
            echo json_encode(['status' => 'error', 'message' => '权限不足']);
            exit;
        }

        $userId = $input['user_id'];
        $userData = $input['user_data'];

        // 验证用户是否存在
        $checkStmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
        $checkStmt->execute([$userId]);
        if (!$checkStmt->fetch()) {
            echo json_encode(['status' => 'error', 'message' => '用户不存在']);
            exit;
        }

        // 更新用户
        $updateStmt = $pdo->prepare("
            UPDATE users
            SET role = ?, is_station_staff = ?
            WHERE id = ?
        ");

        $result = $updateStmt->execute([
            $userData['role'] ?? 'user',
            $userData['is_station_staff'] ?? 0,
            $userId
        ]);

        if ($result) {
            // 记录操作日志
            if (function_exists('logAction')) {
                // 获取当前操作用户的ID
                $operatorStmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
                $operatorStmt->execute([$username]);
                $operator = $operatorStmt->fetch();

                if ($operator) {
                    // 临时设置SESSION以便logAction能获取到用户ID
                    $_SESSION['user'] = ['id' => $operator['id'], 'username' => $username];
                    logAction('更新用户信息', 'users', [
                        'user_id' => $userId,
                        'new_role' => $userData['role'] ?? 'user',
                        'is_station_staff' => $userData['is_station_staff'] ?? 0
                    ]);
                    unset($_SESSION['user']);
                }
            }

            echo json_encode(['status' => 'success', 'message' => '用户更新成功']);
        } else {
            echo json_encode(['status' => 'error', 'message' => '用户更新失败']);
        }

    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '更新用户失败']);
    }
}

/**
 * 删除用户
 */
function deleteUser($input) {
    global $pdo;

    try {
        // 验证token和权限
        if (!isset($input['token']) || !isset($input['username'])) {
            echo json_encode(['status' => 'error', 'message' => '未登录或token无效']);
            exit;
        }

        $username = $input['username'];

        // 获取当前用户角色
        $userStmt = $pdo->prepare("SELECT role FROM users WHERE username = ?");
        $userStmt->execute([$username]);
        $currentUser = $userStmt->fetch();

        if (!$currentUser || ($currentUser['role'] !== 'admin' && $currentUser['role'] !== 'manager')) {
            echo json_encode(['status' => 'error', 'message' => '权限不足']);
            exit;
        }

        $userId = $input['user_id'];

        // 验证用户是否存在
        $checkStmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
        $checkStmt->execute([$userId]);
        $targetUser = $checkStmt->fetch();

        if (!$targetUser) {
            echo json_encode(['status' => 'error', 'message' => '用户不存在']);
            exit;
        }

        // 防止删除自己
        if ($targetUser['username'] === $username) {
            echo json_encode(['status' => 'error', 'message' => '不能删除自己']);
            exit;
        }

        // 删除用户
        $deleteStmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
        $result = $deleteStmt->execute([$userId]);

        if ($result) {
            // 记录操作日志
            if (function_exists('logAction')) {
                // 获取当前操作用户的ID
                $operatorStmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
                $operatorStmt->execute([$username]);
                $operator = $operatorStmt->fetch();

                if ($operator) {
                    // 临时设置SESSION以便logAction能获取到用户ID
                    $_SESSION['user'] = ['id' => $operator['id'], 'username' => $username];
                    logAction('删除用户', 'users', [
                        'user_id' => $userId,
                        'deleted_username' => $targetUser['username']
                    ]);
                    unset($_SESSION['user']);
                }
            }

            echo json_encode(['status' => 'success', 'message' => '用户删除成功']);
        } else {
            echo json_encode(['status' => 'error', 'message' => '用户删除失败']);
        }

    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '删除用户失败']);
    }
}

/**
 * 重置用户密码
 */
function resetUserPassword($input) {
    global $pdo;

    try {
        // 验证token和权限
        if (!isset($input['token']) || !isset($input['username'])) {
            echo json_encode(['status' => 'error', 'message' => '未登录或token无效']);
            exit;
        }

        $username = $input['username'];

        // 获取当前用户角色
        $userStmt = $pdo->prepare("SELECT role FROM users WHERE username = ?");
        $userStmt->execute([$username]);
        $currentUser = $userStmt->fetch();

        if (!$currentUser || ($currentUser['role'] !== 'admin' && $currentUser['role'] !== 'manager')) {
            echo json_encode(['status' => 'error', 'message' => '权限不足']);
            exit;
        }

        $userId = $input['user_id'];

        // 验证用户是否存在
        $checkStmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
        $checkStmt->execute([$userId]);
        $targetUser = $checkStmt->fetch();

        if (!$targetUser) {
            echo json_encode(['status' => 'error', 'message' => '用户不存在']);
            exit;
        }

        // 防止重置自己的密码
        if ($targetUser['username'] === $username) {
            echo json_encode(['status' => 'error', 'message' => '不能重置自己的密码']);
            exit;
        }

        // 重置密码为000000（使用哈希存储）
        $newPassword = '000000';
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

        $resetStmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
        $result = $resetStmt->execute([$hashedPassword, $userId]);

        if ($result) {
            // 记录操作日志
            if (function_exists('logAction')) {
                // 获取当前操作用户的ID
                $operatorStmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
                $operatorStmt->execute([$username]);
                $operator = $operatorStmt->fetch();

                if ($operator) {
                    // 临时设置SESSION以便logAction能获取到用户ID
                    $_SESSION['user'] = ['id' => $operator['id'], 'username' => $username];
                    logAction('重置用户密码', 'users', [
                        'user_id' => $userId,
                        'target_username' => $targetUser['username']
                    ]);
                    unset($_SESSION['user']);
                }
            }

            echo json_encode(['status' => 'success', 'message' => '密码重置成功']);
        } else {
            echo json_encode(['status' => 'error', 'message' => '密码重置失败']);
        }

    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '重置密码失败']);
    }
}

/**
 * 获取个人年度津贴数据
 */
function getPersonalYearlyData($input) {
    global $pdo;

    try {
        // 验证token和权限
        if (!isset($input['token']) || !isset($input['username'])) {
            echo json_encode(['status' => 'error', 'message' => '未登录或token无效']);
            exit;
        }

        if (!isset($input['person_name']) || !isset($input['year'])) {
            echo json_encode(['status' => 'error', 'message' => '参数不完整']);
            exit;
        }

        $username = $input['username'];
        $personName = $input['person_name'];
        $year = intval($input['year']);

        // 获取当前用户角色
        $userStmt = $pdo->prepare("SELECT role, is_station_staff FROM users WHERE username = ?");
        $userStmt->execute([$username]);
        $currentUser = $userStmt->fetch();

        if (!$currentUser) {
            echo json_encode(['status' => 'error', 'message' => '用户不存在']);
            exit;
        }

        // 权限验证：管理员不受限制，其他用户需要一站人员身份
        $isAdmin = ($currentUser['role'] === 'admin' || $currentUser['role'] === 'manager');
        $isStationStaff = ($currentUser['is_station_staff'] == 1);

        if (!$isAdmin && !$isStationStaff) {
            echo json_encode(['status' => 'error', 'message' => '权限不足，需要一站人员身份']);
            exit;
        }

        // 管理员和一站人员都可以查看任何人的个人年度数据

        // 查询个人年度津贴数据
        $sql = "SELECT
                    MONTH(record_date) as month,
                    DAY(record_date) as day,
                    record_date,
                    total_amount,
                    remark
                FROM jintie_records
                WHERE name = ? AND YEAR(record_date) = ?
                ORDER BY record_date DESC";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([$personName, $year]);
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 格式化数据
        $formattedRecords = [];
        foreach ($records as $record) {
            $formattedRecords[] = [
                'month' => sprintf('%02d', $record['month']),
                'date' => $record['record_date'],
                'amount' => number_format($record['total_amount'], 0),
                'remark' => $record['remark'] ?: '-'
            ];
        }

        echo json_encode([
            'status' => 'success',
            'data' => [
                'person_name' => $personName,
                'year' => $year,
                'records' => $formattedRecords,
                'total_count' => count($formattedRecords)
            ]
        ]);

    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '获取个人数据失败']);
    }
}

?>
