<?php
unset($_POST['remark']); // 清理旧字段
$data = json_decode(file_get_contents('php://input'), true);

// 验证必填字段
if (!isset($data['id']) || !is_numeric($data['id'])) {
    die(json_encode(['status'=>'error','message'=>'Invalid ID']));
}

// 计算天数
try {
    $start = new DateTime($data['startDate']);
    $end = new DateTime($data['endDate']);
    $days = $start->diff($end)->days + 1;
} catch(Exception $e) {
    die(json_encode(['status'=>'error','message'=>'Invalid date format']));
}

// 更新数据库
try {
$stmt = $pdo->prepare("
    UPDATE schedules SET
        year = ?,
        month = ?,
        station = ?,
        start_date = ?,
        end_date = ?,
        days = ?
    WHERE id = ?
");
    
    $stmt->execute([
        $start->format('Y'),
        $start->format('n'),
        $data['station'],
        $data['startDate'],
        $data['endDate'],
        $days,
        $data['id']
    ]);
    
    echo json_encode(['status' => 'success']);
} catch(PDOException $e) {
    die(json_encode(['status'=>'error','message'=>'Update failed']));
}