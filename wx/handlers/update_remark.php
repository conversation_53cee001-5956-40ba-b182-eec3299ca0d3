<?php
header('Content-Type: application/json; charset=utf-8');
$data = json_decode(file_get_contents('php://input'), true);

$required = ['year', 'month'];
foreach ($required as $field) {
    if (!isset($data[$field])) {
        die(json_encode(['status'=>'error','message'=>"Missing $field"]));
    }
}

try {
    $stmt = $pdo->prepare("
        INSERT INTO month_remarks (year, month, remark)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE remark = VALUES(remark)
    ");
    $stmt->execute([
        $data['year'], 
        $data['month'],
        $data['remark'] ?? '' // 允许空备注
    ]);
    
    echo json_encode(['status' => 'success']);
} catch(PDOException $e) {
    die(json_encode(['status'=>'error','message'=>'Update failed']));
}