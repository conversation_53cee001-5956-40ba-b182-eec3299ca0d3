<?php
require __DIR__ . '/../config.php';

$username = $_POST['username'] ?? '';
$password = $_POST['password'] ?? '';

if (empty($username) || empty($password)) {
    die(json_encode(['status'=>'error','message'=>'请输入用户名和密码']));
}

try {
    // 检查账户锁定状态
    $stmt = $pdo->prepare("
        SELECT *, 
            IF(lockout_time > NOW(), 1, 0) as is_locked 
        FROM admin 
        WHERE username = ?
    ");
    $stmt->execute([$username]);
    $user = $stmt->fetch();

    if (!$user) {
        die(json_encode(['status'=>'error','message'=>'用户名或密码错误']));
    }

    // 账户被锁定
    if ($user['is_locked']) {
        $remaining = strtotime($user['lockout_time']) - time();
        $minutes = ceil($remaining / 60);
        die(json_encode([
            'status'=>'error',
            'message'=>"账号已锁定，请 $minutes 分钟后再试"
        ]));
    }

    // 验证密码
    if (!password_verify($password, $user['password'])) {
        // 增加失败次数
        $newAttempts = $user['login_attempts'] + 1;
        $lockoutTime = ($newAttempts >= 5) ? date('Y-m-d H:i:s', strtotime('+1 hour')) : null;
        
        $stmt = $pdo->prepare("
            UPDATE admin 
            SET login_attempts = ?, 
                lockout_time = ? 
            WHERE id = ?
        ");
        $stmt->execute([$newAttempts, $lockoutTime, $user['id']]);
        
        $remainingAttempts = 5 - $newAttempts;
        $msg = $remainingAttempts > 0 
            ? "密码错误，还剩 $remainingAttempts 次尝试机会"
            : "账号已锁定1小时";
        die(json_encode(['status'=>'error','message'=>$msg]));
    }

    // 登录成功：重置尝试次数
    $stmt = $pdo->prepare("
        UPDATE admin 
        SET login_attempts = 0, 
            lockout_time = NULL 
        WHERE id = ?
    ");
    $stmt->execute([$user['id']]);

    // 生成安全Token
    $token = bin2hex(random_bytes(16)); // 32位十六进制
$expiresAt = date('Y-m-d\TH:i:s', time() + 86400);  // 直接基于当前时间戳+24小时

    // 存储Token
// 在插入新Token前删除旧Token
$stmt = $pdo->prepare("DELETE FROM tokens WHERE user_id = ?");
$stmt->execute([$user['id']]);

// 再插入新Token
$stmt = $pdo->prepare("
    INSERT INTO tokens (user_id, token, expires_at) 
    VALUES (?, ?, ?)
");
$stmt->execute([$user['id'], $token, $expiresAt]);

echo json_encode([
    'status' => 'success',
    'token' => $token,
    'expires_at' => $expiresAt,
    'user' => $user['username']
], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
} catch(PDOException $e) {
    die(json_encode(['status'=>'error','message'=>'登录失败，请稍后再试']));
}