<?php
// 确保文件开头无任何输出
header('Content-Type: application/json; charset=utf-8');

require __DIR__ . '/../config.php';

$year = $_GET['year'] ?? date('Y');

try {
    $stmt = $pdo->prepare("
        SELECT id, year, month, 
               DATE_FORMAT(start_date, '%Y-%m-%d') as start_date,
               DATE_FORMAT(end_date, '%Y-%m-%d') as end_date,
               station, days 
        FROM schedules 
        WHERE year = ?
        ORDER BY month ASC
    ");
    $stmt->execute([$year]);
    $data = $stmt->fetchAll();
    
    echo json_encode(['status' => 'success', 'data' => $data]);
} catch(PDOException $e) {
    // 记录详细错误日志
    error_log("排班数据查询失败: " . $e->getMessage());
    die(json_encode([
        'status' => 'error',
        'message' => 'Query failed: ' . $e->getMessage() // 返回具体错误
    ]));
}