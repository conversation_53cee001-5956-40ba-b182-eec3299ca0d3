<?php
require __DIR__ . '/../config.php';

// 检查admin表中是否存在login_attempts和lockout_time字段
try {
    // 获取admin表的字段信息
    $stmt = $pdo->prepare("SHOW COLUMNS FROM admin");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $hasLoginAttempts = in_array('login_attempts', $columns);
    $hasLockoutTime = in_array('lockout_time', $columns);
    
    // 如果login_attempts字段不存在，添加它
    if (!$hasLoginAttempts) {
        $pdo->exec("ALTER TABLE admin ADD COLUMN login_attempts INT NOT NULL DEFAULT 0");
        echo json_encode(['status' => 'success', 'message' => '已添加login_attempts字段']);
    }
    
    // 如果lockout_time字段不存在，添加它
    if (!$hasLockoutTime) {
        $pdo->exec("ALTER TABLE admin ADD COLUMN lockout_time DATETIME NULL");
        echo json_encode(['status' => 'success', 'message' => '已添加lockout_time字段']);
    }
    
    if ($hasLoginAttempts && $hasLockoutTime) {
        echo json_encode(['status' => 'success', 'message' => '所需字段已存在']);
    }
    
} catch(PDOException $e) {
    echo json_encode(['status' => 'error', 'message' => '更新数据库结构失败: ' . $e->getMessage()]);
} 