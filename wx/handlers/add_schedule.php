<?php
header('Content-Type: application/json; charset=utf-8');
require __DIR__ . '/../config.php';

$data = json_decode(file_get_contents('php://input'), true);

// 清理旧字段
unset($data['remark']);

$required = ['station', 'startDate', 'endDate'];
foreach ($required as $field) {
    if (empty($data[$field])) {
        die(json_encode(['status'=>'error','message'=>"Missing $field"]));
    }
}

try {
    $start = new DateTime($data['startDate']);
    $end = new DateTime($data['endDate']);
    $days = $start->diff($end)->days + 1;
} catch(Exception $e) {
    die(json_encode(['status'=>'error','message'=>'Invalid date format']));
}

try {
    $stmt = $pdo->prepare("
        INSERT INTO schedules 
        (year, month, station, start_date, end_date, days)
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $start->format('Y'),
        $start->format('n'),
        $data['station'],
        $data['startDate'],
        $data['endDate'],
        $days
    ]);
    
    echo json_encode(['status' => 'success']);
} catch(PDOException $e) {
    die(json_encode(['status'=>'error','message'=>'Insert failed: '.$e->getMessage()]));
}