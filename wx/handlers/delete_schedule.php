<?php
$id = $_POST['id'] ?? 0;

if (!is_numeric($id) || $id <= 0) {
    die(json_encode(['status'=>'error','message'=>'Invalid ID']));
}

try {
    $stmt = $pdo->prepare("DELETE FROM schedules WHERE id = ?");
    $stmt->execute([$id]);
    
    if ($stmt->rowCount() > 0) {
        echo json_encode(['status' => 'success']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Record not found']);
    }
} catch(PDOException $e) {
    die(json_encode(['status'=>'error','message'=>'Delete failed']));
}