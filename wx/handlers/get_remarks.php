<?php
// 确保文件开头无任何空格或空行
header('Content-Type: application/json; charset=utf-8'); // 必须为第一行输出

require __DIR__ . '/../config.php';

$year = $_GET['year'] ?? date('Y');

try {
    $stmt = $pdo->prepare("
        SELECT month, remark 
        FROM month_remarks 
        WHERE year = ?
    ");
    $stmt->execute([$year]);
    $remarks = [];
    while ($row = $stmt->fetch()) {
        $remarks[$row['month']] = $row['remark'];
    }
    echo json_encode([
        'status' => 'success',
        'remarks' => $remarks
    ]);
} catch(PDOException $e) {
    die(json_encode(['status'=>'error','message'=>'Query failed']));
}