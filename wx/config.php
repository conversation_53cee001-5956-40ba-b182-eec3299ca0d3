<?php
date_default_timezone_set('Asia/Shanghai');
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');

// 确保此处配置正确
$db_host = 'localhost';
$db_name = 'sunxiyue_wx';
$db_user = 'sunxiyue_wx';
$db_pass = 'Ab8578369@';

try {
    $pdo = new PDO(
        "mysql:host=$db_host;dbname=$db_name;charset=utf8mb4",
        $db_user,
        $db_pass,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
} catch(PDOException $e) {
    die(json_encode(['status' => 'error', 'message' => '数据库连接失败: ' . $e->getMessage()]));
}