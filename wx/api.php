<?php
require 'config.php'; 

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 处理输入数据
$input = file_get_contents('php://input');
$contentType = $_SERVER['CONTENT_TYPE'] ?? '';
$_POST = $contentType === 'application/json' ? json_decode($input, true) : parse_str($input, $_POST);
$action = $_POST['action'] ?? $_GET['action'] ?? '';

try {
    $pdo = new PDO(
        "mysql:host=$db_host;dbname=$db_name;charset=utf8mb4",
        $db_user,
        $db_pass,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
} catch(PDOException $e) {
    die(json_encode(['status'=>'error','message'=>'数据库连接失败']));
}

// Token 验证函数（修复重复定义问题）
function verifyToken() {
    global $pdo;
    
    $headers = getallheaders();
    if (!isset($headers['Authorization'])) {
        die(json_encode(['status'=>'error','message'=>'缺少授权头信息']));
    }
    
    $token = str_replace('Bearer ', '', $headers['Authorization']);
    
    try {
        // 先清理过期Token
        $pdo->exec("DELETE FROM tokens WHERE expires_at < NOW()");
        
        $stmt = $pdo->prepare("
            SELECT tokens.*, admin.username  
            FROM tokens 
            JOIN admin ON tokens.user_id  = admin.id  
            WHERE token = ? 
            AND expires_at > NOW()
        ");
        $stmt->execute([$token]);
        $tokenData = $stmt->fetch();

        if (!$tokenData) {
            die(json_encode(['status'=>'error','message'=>'无效或已过期的令牌']));
        }

        // 更新Token有效期
        $currentExpiry = strtotime($tokenData['expires_at']);
        if ($currentExpiry - time() < 21600) {
            $newExpiry = date('Y-m-d H:i:s', time() + 86400);
            $stmt = $pdo->prepare("
                UPDATE tokens 
                SET expires_at = ? 
                WHERE id = ?
            ");
            $stmt->execute([$newExpiry, $tokenData['id']]);
        }
        return $tokenData;
    } catch(PDOException $e) {
        error_log("Token verification error: " . $e->getMessage());
        die(json_encode(['status'=>'error','message'=>'令牌验证失败']));
    }
}

// 接口路由
switch($action) {
    case 'login':
        require 'handlers/login.php';
        break;
        
    case 'get_data':
        require 'handlers/get_data.php';
        break;
        
    case 'add_schedule':
        verifyToken(); // 需要登录验证
        require 'handlers/add_schedule.php';
        break;
        
    case 'delete_schedule':
        verifyToken(); // 需要登录验证
        require 'handlers/delete_schedule.php';
        break;
        
    case 'update_schedule':
        verifyToken(); // 需要登录验证
        require 'handlers/update_schedule.php';
        break;
        
    case 'update_remark':
        verifyToken();
        require 'handlers/update_remark.php';
        break;

    case 'get_remarks':
        require 'handlers/get_remarks.php';
        break;
    
    case 'verify_token':
        $tokenData = verifyToken();
        // 返回新的有效期（兼容iOS格式）
        $newExpiry = date('Y-m-d\TH:i:s', time() + 86400);
        echo json_encode([
            'status' => 'success',
            'new_expiry' => $newExpiry
        ]);
        exit;
        break;
        
    case 'update_admin_table':
        // 此操作需要特殊密钥验证，防止未授权访问
        $secretKey = $_POST['secret_key'] ?? '';
        if ($secretKey !== 'your_secret_key_here') {
            die(json_encode(['status' => 'error', 'message' => '未授权访问']));
        }
        require 'handlers/update_admin_table.php';
        break;
        
    default:
        die(json_encode([
            'status' => 'error',
            'message' => '无效的操作',
            'available_actions' => ['login', 'get_data', 'add_schedule', 'delete_schedule', 'update_schedule']
        ]));
}