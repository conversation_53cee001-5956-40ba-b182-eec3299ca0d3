# 微信功能统一API接口文档

## 概述

微信功能统一API整合了微信登录和微信绑定管理功能，提供完整的微信相关服务。包括用户登录、账号绑定、绑定管理等功能。现在已经支持显示用户真实姓名而不是微信昵称。

## 数据库更新

系统已为`users`表添加了`real_name`字段来存储用户真实姓名：

```sql
ALTER TABLE `users` ADD COLUMN `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名' AFTER `username`;
```

## API端点

### 统一入口
```
/api/wechat_api.php
```

### 旧版入口（仍然可用）
```
/api/wechat_login_api.php     - 微信登录功能
/api/wechat_bindings.php      - 微信绑定管理功能
```

### 认证要求
- **微信登录功能**: 无需特殊权限
- **绑定管理功能**: 需要管理员权限（admin角色）和有效的会话登录状态

## API接口

## 一、微信登录功能

### 1. 微信登录
**URL：** `/api/wechat_api.php?action=wechat_login`

### 2. 绑定系统账号
**URL：** `/api/wechat_api.php?action=bind_account`

### 3. 验证Token
**URL：** `/api/wechat_api.php?action=verify_token`

### 4. 退出登录
**URL：** `/api/wechat_api.php?action=logout`

*详细的微信登录API文档请参考原有的wechat_login_api.php文档*

## 二、微信绑定管理功能

### 1. 获取绑定列表

**请求方式：** GET

**URL：** `/api/wechat_api.php?action=bindings_list`

**参数：**
- `page` (可选): 页码，默认为1
- `search` (可选): 搜索关键词，支持搜索用户名、真实姓名、微信昵称

**示例请求：**
```javascript
fetch('/api/wechat_api.php?action=bindings_list&page=1&search=张三')
```

**响应格式：**
```json
{
    "success": true,
    "data": {
        "bindings": [
            {
                "binding_id": 1,
                "bind_type": "manual",
                "bind_time": "2024-01-01 10:00:00",
                "bind_ip": "***********",
                "is_active": 1,
                "user_id": 11,
                "username": "牛伟亮",
                "real_name": "牛伟亮",
                "role": "user",
                "is_station_staff": 0,
                "wechat_user_id": 1,
                "openid": "wx_openid_123",
                "nickname": "微信昵称",
                "avatar_url": "https://...",
                "city": "北京",
                "province": "北京",
                "display_name": "牛伟亮",
                "role_text": "普通用户",
                "bind_type_text": "手动绑定",
                "status_text": "激活"
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_items": 10,
            "items_per_page": 20,
            "total_pages": 1
        },
        "stats": {
            "active_bindings": 8,
            "total_wechat_users": 10,
            "active_tokens": 5
        }
    }
}
```

### 2. 解绑微信账号

**请求方式：** POST

**URL：** `/api/wechat_api.php?action=bindings_unbind`

**请求头：**
```
Content-Type: application/json
```

**请求体：**
```json
{
    "binding_id": 1
}
```

**示例请求：**
```javascript
fetch('/api/wechat_api.php?action=bindings_unbind', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        binding_id: 1
    })
})
```

**响应格式：**
```json
{
    "success": true,
    "message": "微信账号解绑成功"
}
```

## 错误响应

当请求失败时，API会返回错误信息：

```json
{
    "success": false,
    "message": "错误描述"
}
```

常见错误码：
- `403`: 权限不足，只有管理员可以访问
- `404`: 未找到请求的操作
- `405`: 不支持的请求方法
- `400`: 请求参数错误
- `500`: 服务器内部错误

## 前端对接示例

### 1. 获取绑定列表

```javascript
async function loadBindings(page = 1, search = '') {
    try {
        const params = new URLSearchParams({
            action: 'list',
            page: page
        });
        
        if (search) {
            params.append('search', search);
        }
        
        const response = await fetch(`/api/wechat_bindings.php?${params}`);
        const data = await response.json();
        
        if (data.success) {
            // 处理绑定列表数据
            console.log('绑定列表:', data.data.bindings);
            console.log('分页信息:', data.data.pagination);
            console.log('统计信息:', data.data.stats);
        } else {
            console.error('获取失败:', data.message);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}
```

### 2. 解绑微信账号

```javascript
async function unbindWechat(bindingId) {
    try {
        const response = await fetch('/api/wechat_bindings.php?action=unbind', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                binding_id: bindingId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            console.log('解绑成功:', data.message);
            // 重新加载列表
            loadBindings();
        } else {
            console.error('解绑失败:', data.message);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}
```

## 数据字段说明

### 绑定关系字段

- `binding_id`: 绑定关系ID
- `bind_type`: 绑定类型（manual/auto）
- `bind_time`: 绑定时间
- `is_active`: 是否激活（1激活，0禁用）
- `username`: 系统用户名
- `real_name`: 用户真实姓名（新增字段）
- `display_name`: 显示名称（使用username）
- `role`: 用户角色（admin/manager/user）
- `role_text`: 角色文本描述
- `nickname`: 微信昵称
- `avatar_url`: 微信头像URL
- `city`: 城市
- `province`: 省份

### 统计信息字段

- `active_bindings`: 活跃绑定数量
- `total_wechat_users`: 微信用户总数
- `active_tokens`: 在线Token数量

## 注意事项

1. **权限验证**: 所有API接口都需要管理员权限
2. **会话管理**: 确保用户已登录且会话有效
3. **错误处理**: 前端需要妥善处理各种错误情况
4. **数据更新**: 解绑操作后建议重新加载列表数据
5. **显示逻辑**: 系统用户列主要显示username，如果real_name与username不同则在下方显示真实姓名

## API功能对比

| 功能类别 | 统一API | 原有API | 说明 |
|---------|---------|---------|------|
| 微信登录 | `/api/wechat_api.php?action=wechat_login` | `/api/wechat_login_api.php?action=wechat_login` | 功能相同 |
| 账号绑定 | `/api/wechat_api.php?action=bind_account` | `/api/wechat_login_api.php?action=bind_account` | 功能相同 |
| Token验证 | `/api/wechat_api.php?action=verify_token` | `/api/wechat_login_api.php?action=verify_token` | 功能相同 |
| 绑定列表 | `/api/wechat_api.php?action=bindings_list` | `/api/wechat_bindings.php?action=list` | 需要管理员权限 |
| 解绑操作 | `/api/wechat_api.php?action=bindings_unbind` | `/api/wechat_bindings.php?action=unbind` | 需要管理员权限 |

## 完整示例

查看 `/examples/wechat_bindings_frontend.html` 文件，了解完整的前端对接实现示例。
