# 用户管理API接口文档

## 概述

用户管理API专门为微信小程序的管理员功能设计，提供完整的用户管理功能，包括用户的增删改查、密码修改、角色管理等。

## 安全机制

- **权限验证**: 所有接口都需要管理员Token验证
- **Token验证**: 通过微信登录获得的Token，且用户角色必须为admin
- **操作限制**: 管理员不能删除自己、不能修改自己的角色
- **密码安全**: 使用PHP password_hash加密存储

## API端点

### 基础URL
```
/api/user_management_api.php
```

### 认证方式
所有请求都需要在请求体中包含有效的管理员Token：
```json
{
    "token": "your_admin_token_here"
}
```

## API接口列表

### 1. 获取用户列表

**请求方式：** GET

**URL：** `/api/user_management_api.php?action=list`

**参数：**
- `token` (必需): 管理员Token
- `page` (可选): 页码，默认为1
- `search` (可选): 搜索关键词，支持用户名和真实姓名搜索
- `role` (可选): 角色筛选，可选值：admin, manager, user

**示例请求：**
```javascript
const response = await fetch('/api/user_management_api.php?action=list&page=1&search=张三&token=your_token');
```

**响应格式：**
```json
{
    "success": true,
    "data": {
        "users": [
            {
                "id": 1,
                "username": "zhangsan",
                "real_name": "张三",
                "role": "user",
                "role_text": "普通用户",
                "is_station_staff": 0,
                "station_text": "非一站人员",
                "wechat_bindings": 1,
                "has_wechat": true
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_items": 10,
            "items_per_page": 20,
            "total_pages": 1
        }
    }
}
```

### 2. 添加用户

**请求方式：** POST

**URL：** `/api/user_management_api.php?action=add`

**请求体：**
```json
{
    "token": "your_admin_token",
    "username": "newuser",
    "password": "password123",
    "real_name": "新用户",
    "role": "user",
    "is_station_staff": false
}
```

**字段说明：**
- `username`: 用户名，3-50个字符，必须唯一
- `password`: 密码，至少6个字符
- `real_name`: 真实姓名，可选，默认使用用户名
- `role`: 用户角色，可选值：admin, manager, user
- `is_station_staff`: 是否为一站人员，布尔值

**响应格式：**
```json
{
    "success": true,
    "message": "用户添加成功",
    "data": {
        "user_id": 123
    }
}
```

### 3. 删除用户

**请求方式：** POST

**URL：** `/api/user_management_api.php?action=delete`

**请求体：**
```json
{
    "token": "your_admin_token",
    "user_id": 123
}
```

**注意事项：**
- 不能删除自己的账号
- 删除用户会同时删除相关的微信绑定和Token

**响应格式：**
```json
{
    "success": true,
    "message": "用户删除成功"
}
```

### 4. 修改用户密码

**请求方式：** POST

**URL：** `/api/user_management_api.php?action=update_password`

**请求体：**
```json
{
    "token": "your_admin_token",
    "user_id": 123,
    "new_password": "newpassword123"
}
```

**注意事项：**
- 新密码至少6个字符
- 如果修改的是自己的密码，会清除所有Token强制重新登录

**响应格式：**
```json
{
    "success": true,
    "message": "密码修改成功"
}
```

### 5. 修改用户真实姓名

**请求方式：** POST

**URL：** `/api/user_management_api.php?action=update_real_name`

**请求体：**
```json
{
    "token": "your_admin_token",
    "user_id": 123,
    "real_name": "新的真实姓名"
}
```

**字段说明：**
- `real_name`: 真实姓名，不能为空，最多50个字符

**响应格式：**
```json
{
    "success": true,
    "message": "真实姓名修改成功"
}
```

### 6. 修改用户角色

**请求方式：** POST

**URL：** `/api/user_management_api.php?action=update_role`

**请求体：**
```json
{
    "token": "your_admin_token",
    "user_id": 123,
    "role": "manager",
    "is_station_staff": true
}
```

**字段说明：**
- `role`: 用户角色，可选值：admin, manager, user
- `is_station_staff`: 是否为一站人员，布尔值

**注意事项：**
- 不能修改自己的角色

**响应格式：**
```json
{
    "success": true,
    "message": "用户角色修改成功"
}
```

### 7. 获取用户详情

**请求方式：** GET

**URL：** `/api/user_management_api.php?action=detail&user_id=123&token=your_token`

**参数：**
- `user_id`: 用户ID
- `token`: 管理员Token

**响应格式：**
```json
{
    "success": true,
    "data": {
        "id": 123,
        "username": "zhangsan",
        "real_name": "张三",
        "role": "user",
        "role_text": "普通用户",
        "is_station_staff": 0,
        "station_text": "非一站人员",
        "wechat_bindings": 1,
        "has_wechat": true
    }
}
```

## 错误响应

当请求失败时，API会返回错误信息：

```json
{
    "success": false,
    "message": "错误描述"
}
```

常见错误码：
- `403`: 权限不足，Token无效或非管理员
- `404`: 未找到请求的操作
- `400`: 请求参数错误
- `500`: 服务器内部错误

## 数据字段说明

### 用户对象字段

- `id`: 用户ID
- `username`: 用户名
- `real_name`: 真实姓名
- `role`: 用户角色（admin/manager/user）
- `role_text`: 角色文本描述
- `is_station_staff`: 是否为一站人员（0/1）
- `station_text`: 一站人员状态文本
- `wechat_bindings`: 微信绑定数量
- `has_wechat`: 是否有微信绑定

### 角色说明

- `admin`: 管理员，拥有所有权限
- `manager`: 普通管理员，部分管理权限
- `user`: 普通用户，基础权限

## 前端对接示例

### 1. 获取用户列表

```javascript
async function getUserList(page = 1, search = '', role = '') {
    try {
        const params = new URLSearchParams({
            action: 'list',
            page: page,
            token: getAdminToken() // 获取管理员Token的函数
        });
        
        if (search) params.append('search', search);
        if (role) params.append('role', role);
        
        const response = await fetch(`/api/user_management_api.php?${params}`);
        const data = await response.json();
        
        if (data.success) {
            console.log('用户列表:', data.data.users);
            console.log('分页信息:', data.data.pagination);
        } else {
            console.error('获取失败:', data.message);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}
```

### 2. 添加用户

```javascript
async function addUser(userData) {
    try {
        const response = await fetch('/api/user_management_api.php?action=add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                token: getAdminToken(),
                username: userData.username,
                password: userData.password,
                real_name: userData.realName,
                role: userData.role,
                is_station_staff: userData.isStationStaff
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            console.log('添加成功:', data.message);
            return data.data.user_id;
        } else {
            console.error('添加失败:', data.message);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}
```

### 3. 删除用户

```javascript
async function deleteUser(userId) {
    try {
        const response = await fetch('/api/user_management_api.php?action=delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                token: getAdminToken(),
                user_id: userId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            console.log('删除成功:', data.message);
        } else {
            console.error('删除失败:', data.message);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}
```

## 注意事项

1. **Token管理**: 确保Token的安全存储和传输
2. **权限验证**: 所有操作都需要管理员权限
3. **数据验证**: 前端应进行基础数据验证
4. **错误处理**: 妥善处理各种错误情况
5. **用户体验**: 提供适当的加载状态和反馈信息

## 安全建议

1. 使用HTTPS传输敏感数据
2. 定期更换管理员密码
3. 监控异常操作日志
4. 限制管理员账号数量
5. 实施操作审计机制
