// 微信小程序登录页面逻辑示例
const { loginManager } = require('../../utils/login.js');

Page({
  data: {
    isLoggedIn: false,
    userInfo: null,
    loginLoading: false,
    showBindModal: false,
    showAccountModal: false,
    bindLoading: false,
    accountLoginLoading: false,
    bindData: null,
    bindForm: {
      username: '',
      password: ''
    },
    accountForm: {
      username: '',
      password: ''
    }
  },

  onLoad() {
    this.checkLoginStatus();
  },

  onShow() {
    this.checkLoginStatus();
  },

  /**
   * 检查登录状态
   */
  async checkLoginStatus() {
    const isLoggedIn = loginManager.isLoggedIn();
    
    if (isLoggedIn) {
      // 验证token有效性
      const isValid = await loginManager.verifyToken();
      if (isValid) {
        this.setData({
          isLoggedIn: true,
          userInfo: this.formatUserInfo(loginManager.getUserInfo())
        });
      } else {
        this.setData({
          isLoggedIn: false,
          userInfo: null
        });
      }
    } else {
      this.setData({
        isLoggedIn: false,
        userInfo: null
      });
    }
  },

  /**
   * 格式化用户信息显示
   */
  formatUserInfo(userInfo) {
    if (!userInfo) return null;
    
    let roleText = '普通用户';
    if (userInfo.role === 'admin') {
      roleText = '管理员';
    } else if (userInfo.role === 'manager') {
      roleText = '普通管理员';
    }
    
    if (userInfo.is_station_staff) {
      roleText += ' | 一站人员';
    }
    
    return {
      ...userInfo,
      role_text: roleText
    };
  },

  /**
   * 微信登录
   */
  async wechatLogin() {
    this.setData({ loginLoading: true });
    
    try {
      const result = await loginManager.wechatLogin();
      
      if (result.success) {
        // 登录成功
        this.setData({
          isLoggedIn: true,
          userInfo: this.formatUserInfo(result.data.user)
        });
      } else if (result.needBind) {
        // 需要绑定账号
        this.setData({
          showBindModal: true,
          bindData: result.data
        });
      }
    } catch (error) {
      console.error('微信登录失败:', error);
    } finally {
      this.setData({ loginLoading: false });
    }
  },

  /**
   * 显示账号密码登录
   */
  showAccountLogin() {
    this.setData({ showAccountModal: true });
  },

  /**
   * 账号密码登录
   */
  async accountLogin() {
    const { username, password } = this.data.accountForm;
    
    if (!username || !password) {
      wx.showToast({
        title: '请输入用户名和密码',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ accountLoginLoading: true });
    
    try {
      // 这里可以调用传统的账号密码登录接口
      // 或者引导用户使用微信登录后绑定
      wx.showToast({
        title: '建议使用微信登录',
        icon: 'none'
      });
    } catch (error) {
      console.error('账号登录失败:', error);
    } finally {
      this.setData({ accountLoginLoading: false });
    }
  },

  /**
   * 确认绑定账号
   */
  async confirmBind() {
    const { username, password } = this.data.bindForm;
    const { bindData } = this.data;
    
    if (!username || !password) {
      wx.showToast({
        title: '请输入用户名和密码',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ bindLoading: true });
    
    try {
      const result = await loginManager.bindAccount(bindData, username, password);
      
      if (result.success) {
        this.setData({
          isLoggedIn: true,
          userInfo: this.formatUserInfo(result.data.user),
          showBindModal: false,
          bindForm: { username: '', password: '' }
        });
      }
    } catch (error) {
      console.error('绑定失败:', error);
    } finally {
      this.setData({ bindLoading: false });
    }
  },

  /**
   * 退出登录
   */
  async logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          loginManager.clearLoginData();
          this.setData({
            isLoggedIn: false,
            userInfo: null
          });
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 进入主应用
   */
  goToMain() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 表单输入处理
  onBindUsernameInput(e) {
    this.setData({
      'bindForm.username': e.detail.value
    });
  },

  onBindPasswordInput(e) {
    this.setData({
      'bindForm.password': e.detail.value
    });
  },

  onAccountUsernameInput(e) {
    this.setData({
      'accountForm.username': e.detail.value
    });
  },

  onAccountPasswordInput(e) {
    this.setData({
      'accountForm.password': e.detail.value
    });
  },

  // 模态框控制
  hideBindModal() {
    this.setData({ showBindModal: false });
  },

  hideAccountModal() {
    this.setData({ showAccountModal: false });
  },

  stopPropagation() {
    // 阻止事件冒泡
  }
});
