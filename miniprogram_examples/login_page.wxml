<!-- 微信小程序登录页面示例 -->
<view class="login-container">
  <!-- 登录状态显示 -->
  <view wx:if="{{isLoggedIn}}" class="user-info">
    <view class="avatar-section">
      <image class="avatar" src="{{userInfo.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      <text class="username">{{userInfo.username}}</text>
      <text class="role">{{userInfo.role_text}}</text>
    </view>
    
    <view class="user-actions">
      <button class="action-btn primary" bindtap="goToMain">进入应用</button>
      <button class="action-btn secondary" bindtap="logout">退出登录</button>
    </view>
  </view>

  <!-- 未登录状态 -->
  <view wx:else class="login-section">
    <view class="logo-section">
      <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
      <text class="app-name">平台常用工具</text>
      <text class="app-desc">请登录以使用完整功能</text>
    </view>

    <!-- 微信登录按钮 -->
    <view class="login-methods">
      <button class="login-btn wechat" bindtap="wechatLogin" loading="{{loginLoading}}">
        <image class="btn-icon" src="/images/wechat-icon.png"></image>
        <text>微信快速登录</text>
      </button>
      
      <view class="divider">
        <text>或</text>
      </view>
      
      <button class="login-btn account" bindtap="showAccountLogin">
        <text>使用账号密码登录</text>
      </button>
    </view>
  </view>

  <!-- 账号绑定弹窗 -->
  <view wx:if="{{showBindModal}}" class="modal-overlay" bindtap="hideBindModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">绑定系统账号</text>
        <text class="modal-close" bindtap="hideBindModal">×</text>
      </view>
      
      <view class="modal-body">
        <view class="bind-info">
          <text class="bind-text">您的微信账号尚未绑定系统账号</text>
          <text class="bind-desc">请输入您的系统账号信息进行绑定</text>
        </view>
        
        <view class="form-group">
          <text class="label">用户名</text>
          <input class="input" placeholder="请输入用户名" value="{{bindForm.username}}" bindinput="onBindUsernameInput" />
        </view>
        
        <view class="form-group">
          <text class="label">密码</text>
          <input class="input" type="password" placeholder="请输入密码" value="{{bindForm.password}}" bindinput="onBindPasswordInput" />
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="hideBindModal">取消</button>
        <button class="modal-btn confirm" bindtap="confirmBind" loading="{{bindLoading}}">绑定</button>
      </view>
    </view>
  </view>

  <!-- 账号密码登录弹窗 -->
  <view wx:if="{{showAccountModal}}" class="modal-overlay" bindtap="hideAccountModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">账号密码登录</text>
        <text class="modal-close" bindtap="hideAccountModal">×</text>
      </view>
      
      <view class="modal-body">
        <view class="form-group">
          <text class="label">用户名</text>
          <input class="input" placeholder="请输入用户名" value="{{accountForm.username}}" bindinput="onAccountUsernameInput" />
        </view>
        
        <view class="form-group">
          <text class="label">密码</text>
          <input class="input" type="password" placeholder="请输入密码" value="{{accountForm.password}}" bindinput="onAccountPasswordInput" />
        </view>
        
        <view class="login-tip">
          <text>提示：首次使用建议先用微信登录并绑定账号</text>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="hideAccountModal">取消</button>
        <button class="modal-btn confirm" bindtap="accountLogin" loading="{{accountLoginLoading}}">登录</button>
      </view>
    </view>
  </view>
</view>
