<?php
require '../includes/config.php';

// 允许管理员和普通管理员访问
if (!isset($_SESSION['user']) || ($_SESSION['user']['role'] != 'admin' && $_SESSION['user']['role'] != 'manager')) {
    header("HTTP/1.1 403 Forbidden");
    exit('无权访问');
}

// 检查并更新 login_attempts 表结构
function updateLoginAttemptsTable($pdo) {
    try {
        // 检查 module 列是否存在
        $stmt = $pdo->query("SHOW COLUMNS FROM login_attempts LIKE 'module'");
        if ($stmt->rowCount() == 0) {
            // 添加 module 列
            $pdo->exec("ALTER TABLE login_attempts ADD COLUMN module VARCHAR(50) DEFAULT 'unknown' AFTER is_success");
            // 尝试添加索引（如果不存在的话）
            try {
                $pdo->exec("ALTER TABLE login_attempts ADD INDEX idx_username_time (username, attempt_time)");
            } catch (PDOException $e) {
                // 索引可能已存在，忽略错误
            }
            try {
                $pdo->exec("ALTER TABLE login_attempts ADD INDEX idx_ip_time (ip_address, attempt_time)");
            } catch (PDOException $e) {
                // 索引可能已存在，忽略错误
            }
        }
    } catch (PDOException $e) {
        error_log("更新 login_attempts 表结构失败: " . $e->getMessage());
    }
}

// 更新表结构
updateLoginAttemptsTable($pdo);

// 分页查询
$page = max(1, intval($_GET['page'] ?? 1));
$itemsPerPage = 20; // 每页显示20条记录
$offset = ($page - 1) * $itemsPerPage;

// 搜索条件
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$whereClause = '';
$params = [];

if (!empty($search)) {
    $whereClause = "WHERE u.username LIKE ? OR l.action LIKE ? OR l.target LIKE ?";
    $params = ["%$search%", "%$search%", "%$search%"];
}

// 查询总记录数
$countSql = "SELECT COUNT(*) FROM operation_logs l LEFT JOIN users u ON l.user_id = u.id $whereClause";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$total = $countStmt->fetchColumn();

// 查询日志数据
$sql = "SELECT l.*, u.username
        FROM operation_logs l
        LEFT JOIN users u ON l.user_id = u.id
        $whereClause
        ORDER BY l.created_at DESC
        LIMIT ? OFFSET ?";
$stmt = $pdo->prepare($sql);
$params[] = $itemsPerPage;
$params[] = $offset;
$stmt->execute($params);
$logs = $stmt->fetchAll();

// 为每个日志记录添加登录模块信息（如果是登录相关操作）
foreach ($logs as &$log) {
    $log['login_module_info'] = null;

    // 检查是否是登录相关操作
    if (stripos($log['action'], '登录') !== false || stripos($log['action'], 'login') !== false) {
        // 获取用户名：优先使用 JOIN 的结果，否则从 details JSON 中提取
        $username = null;
        if (!empty($log['username'])) {
            $username = $log['username'];
        } else {
            // 从 details JSON 中提取用户名
            $details = json_decode($log['details'], true);
            if (is_array($details) && isset($details['username'])) {
                $username = $details['username'];
            }
        }

        if (!$username) {
            continue; // 如果没有用户名，跳过
        }


        try {
            // 检查 module 列是否存在
            $checkColumnStmt = $pdo->query("SHOW COLUMNS FROM login_attempts LIKE 'module'");
            $hasModuleColumn = $checkColumnStmt->rowCount() > 0;

            if ($hasModuleColumn) {
                // 根据时间匹配查找最接近的登录记录
                $loginInfoStmt = $pdo->prepare("SELECT
                    username,
                    ip_address,
                    attempt_time,
                    is_success,
                    COALESCE(module, 'unknown') as module,
                    CASE
                        WHEN COALESCE(module, 'unknown') = 'tag_query' THEN '仪表位号及阀门编号查询'
                        WHEN COALESCE(module, 'unknown') = 'jintie_query' THEN '一站津贴查询'
                        WHEN COALESCE(module, 'unknown') = 'shift_schedule' THEN '倒班时间查询'
                        WHEN COALESCE(module, 'unknown') = 'admin_backend' THEN '后台管理系统'
                        ELSE '未知模块'
                    END as module_name,
                    ABS(TIMESTAMPDIFF(SECOND, attempt_time, ?)) as time_diff
                FROM login_attempts
                WHERE username = ?
                AND is_success = 1
                AND ABS(TIMESTAMPDIFF(SECOND, attempt_time, ?)) <= 300
                ORDER BY time_diff ASC
                LIMIT 1");

                $loginInfoStmt->execute([$log['created_at'], $username, $log['created_at']]);
                $loginInfo = $loginInfoStmt->fetch();

                if ($loginInfo) {
                    $log['login_module_info'] = "登录模块: " . $loginInfo['module_name'] . "\n" .
                                               "IP地址: " . $loginInfo['ip_address'] . "\n" .
                                               "登录结果: " . ($loginInfo['is_success'] ? '成功' : '失败');
                }
            } else {
                // 如果没有 module 列，查找时间最接近的记录
                $loginInfoStmt = $pdo->prepare("SELECT
                    ip_address,
                    is_success
                FROM login_attempts
                WHERE username = ?
                ORDER BY ABS(TIMESTAMPDIFF(SECOND, attempt_time, ?)) ASC
                LIMIT 1");

                $loginInfoStmt->execute([$log['username'], $log['created_at']]);
                $loginInfo = $loginInfoStmt->fetch();

                if ($loginInfo) {
                    $log['login_module_info'] = "登录模块: 未知模块\n" .
                                               "IP地址: " . $loginInfo['ip_address'] . "\n" .
                                               "登录结果: " . ($loginInfo['is_success'] ? '成功' : '失败');
                }
            }
        } catch (PDOException $e) {
            error_log("查询登录模块信息失败: " . $e->getMessage());
        }
    }
}


?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志</title>
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/apple-style.css" rel="stylesheet">
    <style>
        /* 操作日志页面特定样式 */
        
        .badge-action {
            padding: 4px 8px;
            font-size: 11px;
            font-weight: 500;
            border-radius: 6px;
            letter-spacing: 0.06em;
        }

        .badge-create {
            background-color: rgba(52, 199, 89, 0.1);
            color: #34c759;
        }

        .badge-update {
            background-color: rgba(255, 149, 0, 0.1);
            color: #ff9500;
        }

        .badge-delete {
            background-color: rgba(255, 59, 48, 0.1);
            color: #ff3b30;
        }

        .badge-login {
            background-color: rgba(0, 122, 255, 0.1);
            color: #007aff;
        }

        .badge-other {
            background-color: rgba(142, 142, 147, 0.1);
            color: #8e8e93;
        }
        
        .pagination {
            justify-content: center;
            margin-top: 20px;
        }
        
        .page-link {
            color: #4b6cb7;
        }
        
        .page-item.active .page-link {
            background-color: #4b6cb7;
            border-color: #4b6cb7;
        }
        
        .log-user {
            font-weight: 500;
            color: #495057;
        }
        
        .log-time {
            color: #6c757d;
            font-size: 0.85rem;
        }
        
        .log-action {
            color: #212529;
            word-break: break-word;
        }
        
        .details-cell {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .details-cell:hover {
            white-space: normal;
            word-break: break-word;
        }

        /* 排班信息样式 */
        .alert-shift {
            background-color: rgba(52, 199, 89, 0.1);
            border: 1px solid rgba(52, 199, 89, 0.2);
            color: #1d4e2a;
        }

        .shift-info-item {
            margin-bottom: 2px;
        }

        .shift-station-一站 {
            color: #007aff;
            font-weight: 600;
        }

        .shift-station-二站 {
            color: #34c759;
            font-weight: 600;
        }

        /* 用户管理信息样式 */
        .alert-user {
            background-color: rgba(0, 122, 255, 0.1);
            border: 1px solid rgba(0, 122, 255, 0.2);
            color: #1a365d;
        }

        .user-info-item {
            margin-bottom: 2px;
        }

        .user-role-admin {
            color: #ff3b30;
            font-weight: 600;
        }

        .user-role-manager {
            color: #ff9500;
            font-weight: 600;
        }

        .user-role-user {
            color: #34c759;
            font-weight: 600;
        }

        /* 登录/退出信息样式 */
        .alert-login {
            background-color: rgba(0, 122, 255, 0.1);
            border: 1px solid rgba(0, 122, 255, 0.2);
            color: #1a365d;
        }

        .alert-logout {
            background-color: rgba(255, 149, 0, 0.1);
            border: 1px solid rgba(255, 149, 0, 0.2);
            color: #7c2d12;
        }

        .auth-info-item {
            margin-bottom: 2px;
        }

        .module-tag {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-left: 4px;
        }

        .module-tag-admin {
            background-color: rgba(255, 59, 48, 0.1);
            color: #ff3b30;
        }

        .module-tag-jintie {
            background-color: rgba(52, 199, 89, 0.1);
            color: #34c759;
        }

        .module-tag-shift {
            background-color: rgba(0, 122, 255, 0.1);
            color: #007aff;
        }

        .module-tag-tag {
            background-color: rgba(255, 149, 0, 0.1);
            color: #ff9500;
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><i class="fas fa-clipboard-list mr-2"></i>操作日志</h4>
        </div>
        
        <div class="card-body">
            <!-- 搜索表单 -->
            <form class="mb-4" method="get" action="operation_logs.php">
                <div class="input-group">
                    <input type="text" name="search" class="form-control" 
                           placeholder="搜索用户名、操作类型或目标" value="<?= htmlspecialchars($search) ?>">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <?php if(!empty($search)): ?>
                            <a href="operation_logs.php" class="btn btn-outline-danger">
                                <i class="fas fa-times"></i> 清除
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
            


            <!-- 结果统计 -->
            <div class="mb-3">
                共找到 <strong><?= $total ?></strong> 条操作记录
            </div>
            
            <!-- 日志表格 -->
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr class="text-center">
                            <th style="width: 15%">时间</th>
                            <th style="width: 10%">操作用户</th>
                            <th style="width: 15%">操作类型</th>
                            <th style="width: 15%">操作目标</th>
                            <th style="width: 45%">详细信息</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(count($logs) > 0): ?>
                            <?php foreach($logs as $log): ?>
                                <tr>
                                    <td class="text-center">
                                        <?= date('Y-m-d H:i:s', strtotime($log['created_at'])) ?>
                                    </td>
                                    <td class="text-center">
                                        <span class="log-user">
                                            <i class="fas fa-user-circle mr-1"></i>
                                            <?= !empty($log['username']) ? htmlspecialchars($log['username']) : '系统' ?>
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <?php
                                        $actionLower = strtolower($log['action']);
                                        $badgeClass = 'badge-other';
                                        $icon = 'fa-cog';
                                        
                                        if (strpos($actionLower, 'create') !== false || strpos($actionLower, '创建') !== false || strpos($actionLower, '添加') !== false) {
                                            $badgeClass = 'badge-create';
                                            $icon = 'fa-plus-circle';
                                        } elseif (strpos($actionLower, 'update') !== false || strpos($actionLower, '更新') !== false || strpos($actionLower, '修改') !== false) {
                                            $badgeClass = 'badge-update';
                                            $icon = 'fa-edit';
                                        } elseif (strpos($actionLower, 'delete') !== false || strpos($actionLower, '删除') !== false) {
                                            $badgeClass = 'badge-delete';
                                            $icon = 'fa-trash-alt';
                                        } elseif (strpos($actionLower, 'login') !== false || strpos($actionLower, '登录') !== false) {
                                            $badgeClass = 'badge-login';
                                            $icon = 'fa-sign-in-alt';
                                        } elseif (strpos($actionLower, 'logout') !== false || strpos($actionLower, '退出') !== false) {
                                            $badgeClass = 'badge-update';
                                            $icon = 'fa-sign-out-alt';
                                        }
                                        ?>
                                        <span class="badge badge-action <?= $badgeClass ?>">
                                            <i class="fas <?= $icon ?> mr-1"></i>
                                            <?= htmlspecialchars($log['action']) ?>
                                        </span>
                                    </td>
                                    <td><?= htmlspecialchars($log['target']) ?></td>
                                    <td class="details-cell">
                                        <?php
                                        // 不再显示原有的登录模块信息，改为统一的格式化显示

                                        // 显示格式化信息（排班管理、用户管理、登录退出）
                                        $details = json_decode($log['details'], true);
                                        $isShiftOperation = ($log['target'] === 'shift_schedule' || $log['target'] === 'shift_schedules');
                                        $isUserOperation = ($log['target'] === 'users');
                                        $isLoginOperation = ($log['target'] === 'login' && (stripos($log['action'], '登录') !== false || stripos($log['action'], 'login') !== false));
                                        $isLogoutOperation = ($log['target'] === 'logout' && (stripos($log['action'], '退出') !== false || stripos($log['action'], 'logout') !== false));

                                        if ($isShiftOperation && is_array($details)) {
                                            echo '<div class="alert alert-shift mb-2" style="font-size: 0.85rem; padding: 8px 12px;">';
                                            echo '<i class="fas fa-calendar-alt mr-1"></i>';
                                            echo '<strong>排班信息:</strong><br>';

                                            // 格式化显示排班信息
                                            $formattedInfo = [];

                                            if (isset($details['id'])) {
                                                $formattedInfo[] = '<span class="shift-info-item"><i class="fas fa-hashtag mr-1" style="font-size: 0.7rem;"></i>记录ID: <strong>' . $details['id'] . '</strong></span>';
                                            }

                                            if (isset($details['station'])) {
                                                $stationName = $details['station'] === '一站' ? '一站' : '二站';
                                                $stationClass = 'shift-station-' . $stationName;
                                                $formattedInfo[] = '<span class="shift-info-item"><i class="fas fa-building mr-1" style="font-size: 0.7rem;"></i>班次: <span class="' . $stationClass . '">' . $stationName . '</span></span>';
                                            }

                                            if (isset($details['startDate']) && isset($details['endDate'])) {
                                                $formattedInfo[] = '<span class="shift-info-item"><i class="fas fa-clock mr-1" style="font-size: 0.7rem;"></i>时间段: <strong>' . $details['startDate'] . '</strong> 至 <strong>' . $details['endDate'] . '</strong></span>';
                                            } elseif (isset($details['start_date']) && isset($details['end_date'])) {
                                                $formattedInfo[] = '<span class="shift-info-item"><i class="fas fa-clock mr-1" style="font-size: 0.7rem;"></i>时间段: <strong>' . $details['start_date'] . '</strong> 至 <strong>' . $details['end_date'] . '</strong></span>';
                                            }

                                            if (isset($details['days'])) {
                                                $formattedInfo[] = '<span class="shift-info-item"><i class="fas fa-calendar-day mr-1" style="font-size: 0.7rem;"></i>天数: <strong>' . $details['days'] . '</strong> 天</span>';
                                            }

                                            if (isset($details['year']) && isset($details['month'])) {
                                                $formattedInfo[] = '<span class="shift-info-item"><i class="fas fa-calendar mr-1" style="font-size: 0.7rem;"></i>年月: <strong>' . $details['year'] . '</strong>年<strong>' . $details['month'] . '</strong>月</span>';
                                            }

                                            if (isset($details['remark'])) {
                                                $remarkText = $details['remark'] ?: '无';
                                                $formattedInfo[] = '<span class="shift-info-item"><i class="fas fa-sticky-note mr-1" style="font-size: 0.7rem;"></i>备注: <em>' . htmlspecialchars($remarkText) . '</em></span>';
                                            }

                                            echo implode('<br>', $formattedInfo);
                                            echo '</div>';
                                        } elseif ($isUserOperation && is_array($details)) {
                                            // 显示用户管理信息
                                            echo '<div class="alert alert-user mb-2" style="font-size: 0.85rem; padding: 8px 12px;">';
                                            echo '<i class="fas fa-user-cog mr-1"></i>';
                                            echo '<strong>用户信息:</strong><br>';

                                            // 格式化显示用户信息
                                            $formattedInfo = [];

                                            if (isset($details['username'])) {
                                                $formattedInfo[] = '<span class="user-info-item"><i class="fas fa-user mr-1" style="font-size: 0.7rem;"></i>用户名: <strong>' . htmlspecialchars($details['username']) . '</strong></span>';
                                            }

                                            if (isset($details['role'])) {
                                                $roleNames = [
                                                    'admin' => '超级管理员',
                                                    'manager' => '普通管理员',
                                                    'user' => '普通用户'
                                                ];
                                                $roleName = $roleNames[$details['role']] ?? $details['role'];
                                                $roleClass = 'user-role-' . $details['role'];
                                                $formattedInfo[] = '<span class="user-info-item"><i class="fas fa-user-tag mr-1" style="font-size: 0.7rem;"></i>角色: <span class="' . $roleClass . '">' . $roleName . '</span></span>';
                                            }

                                            if (isset($details['new_role'])) {
                                                $roleNames = [
                                                    'admin' => '超级管理员',
                                                    'manager' => '普通管理员',
                                                    'user' => '普通用户'
                                                ];
                                                $newRoleName = $roleNames[$details['new_role']] ?? $details['new_role'];
                                                $newRoleClass = 'user-role-' . $details['new_role'];
                                                $formattedInfo[] = '<span class="user-info-item"><i class="fas fa-exchange-alt mr-1" style="font-size: 0.7rem;"></i>新角色: <span class="' . $newRoleClass . '">' . $newRoleName . '</span></span>';
                                            }

                                            if (isset($details['id']) || isset($details['user_id'])) {
                                                $userId = $details['id'] ?? $details['user_id'];
                                                $formattedInfo[] = '<span class="user-info-item"><i class="fas fa-hashtag mr-1" style="font-size: 0.7rem;"></i>用户ID: <strong>' . $userId . '</strong></span>';
                                            }

                                            if (isset($details['new_user_id'])) {
                                                $formattedInfo[] = '<span class="user-info-item"><i class="fas fa-plus-circle mr-1" style="font-size: 0.7rem;"></i>新用户ID: <strong>' . $details['new_user_id'] . '</strong></span>';
                                            }

                                            // 显示一站人员身份信息
                                            if (isset($details['is_station_staff'])) {
                                                $stationStatus = $details['is_station_staff'] ? '是' : '否';
                                                $statusColor = $details['is_station_staff'] ? '#34c759' : '#8e8e93';
                                                $formattedInfo[] = '<span class="user-info-item"><i class="fas fa-building mr-1" style="font-size: 0.7rem;"></i>一站人员: <span style="color: ' . $statusColor . '; font-weight: 600;">' . $stationStatus . '</span></span>';
                                            }

                                            echo implode('<br>', $formattedInfo);
                                            echo '</div>';
                                        } elseif ($isLoginOperation && is_array($details)) {
                                            // 显示登录信息
                                            echo '<div class="alert alert-login mb-2" style="font-size: 0.85rem; padding: 8px 12px;">';
                                            echo '<i class="fas fa-sign-in-alt mr-1"></i>';
                                            echo '<strong>登录信息:</strong><br>';

                                            // 格式化显示登录信息
                                            $formattedInfo = [];

                                            if (isset($details['username'])) {
                                                $formattedInfo[] = '<span class="auth-info-item"><i class="fas fa-user mr-1" style="font-size: 0.7rem;"></i>用户名: <strong>' . htmlspecialchars($details['username']) . '</strong></span>';
                                            }

                                            // 查询登录模块信息
                                            if (!empty($log['login_module_info'])) {
                                                $moduleInfo = explode("\n", $log['login_module_info']);
                                                foreach ($moduleInfo as $info) {
                                                    if (strpos($info, '登录模块:') !== false) {
                                                        $moduleName = trim(str_replace('登录模块:', '', $info));
                                                        $moduleClass = 'module-tag-admin'; // 默认
                                                        if (strpos($moduleName, '津贴') !== false) $moduleClass = 'module-tag-jintie';
                                                        elseif (strpos($moduleName, '倒班') !== false) $moduleClass = 'module-tag-shift';
                                                        elseif (strpos($moduleName, '位号') !== false) $moduleClass = 'module-tag-tag';

                                                        $formattedInfo[] = '<span class="auth-info-item"><i class="fas fa-desktop mr-1" style="font-size: 0.7rem;"></i>登录模块: <span class="module-tag ' . $moduleClass . '">' . htmlspecialchars($moduleName) . '</span></span>';
                                                    } elseif (strpos($info, 'IP地址:') !== false) {
                                                        $ipAddress = trim(str_replace('IP地址:', '', $info));
                                                        $formattedInfo[] = '<span class="auth-info-item"><i class="fas fa-globe mr-1" style="font-size: 0.7rem;"></i>IP地址: <strong>' . htmlspecialchars($ipAddress) . '</strong></span>';
                                                    } elseif (strpos($info, '登录结果:') !== false) {
                                                        $result = trim(str_replace('登录结果:', '', $info));
                                                        $resultColor = $result === '成功' ? '#34c759' : '#ff3b30';
                                                        $resultIcon = $result === '成功' ? 'fa-check-circle' : 'fa-times-circle';
                                                        $formattedInfo[] = '<span class="auth-info-item"><i class="fas ' . $resultIcon . ' mr-1" style="font-size: 0.7rem; color: ' . $resultColor . ';"></i>登录结果: <span style="color: ' . $resultColor . '; font-weight: 600;">' . htmlspecialchars($result) . '</span></span>';
                                                    }
                                                }
                                            }

                                            echo implode('<br>', $formattedInfo);
                                            echo '</div>';
                                        } elseif ($isLogoutOperation && is_array($details)) {
                                            // 显示退出信息
                                            echo '<div class="alert alert-logout mb-2" style="font-size: 0.85rem; padding: 8px 12px;">';
                                            echo '<i class="fas fa-sign-out-alt mr-1"></i>';
                                            echo '<strong>退出信息:</strong><br>';

                                            // 格式化显示退出信息
                                            $formattedInfo = [];

                                            if (isset($details['username'])) {
                                                $formattedInfo[] = '<span class="auth-info-item"><i class="fas fa-user mr-1" style="font-size: 0.7rem;"></i>用户名: <strong>' . htmlspecialchars($details['username']) . '</strong></span>';
                                            }

                                            $formattedInfo[] = '<span class="auth-info-item"><i class="fas fa-check-circle mr-1" style="font-size: 0.7rem; color: #34c759;"></i>退出状态: <span style="color: #34c759; font-weight: 600;">成功</span></span>';

                                            echo implode('<br>', $formattedInfo);
                                            echo '</div>';
                                        } elseif (is_array($details)) {
                                            // 显示其他操作的详细信息
                                            echo '<pre class="mb-0" style="font-size: 0.85rem;">';
                                            foreach ($details as $key => $value) {
                                                if (is_array($value)) {
                                                    echo htmlspecialchars($key) . ': ' . htmlspecialchars(json_encode($value, JSON_UNESCAPED_UNICODE)) . "\n";
                                                } else {
                                                    echo htmlspecialchars($key) . ': ' . htmlspecialchars($value ?? '') . "\n";
                                                }
                                            }
                                            echo '</pre>';
                                        } else {
                                            echo htmlspecialchars($log['details']);
                                        }
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <i class="fas fa-info-circle mr-1 text-info"></i> 没有找到符合条件的操作日志
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <?php if($total > $itemsPerPage): ?>
                <nav>
                    <ul class="pagination">
                        <?php 
                        $totalPages = ceil($total / $itemsPerPage);
                        $maxPagesToShow = 5;
                        $startPage = max(1, min($page - floor($maxPagesToShow / 2), $totalPages - $maxPagesToShow + 1));
                        $endPage = min($totalPages, $startPage + $maxPagesToShow - 1);
                        
                        // 构建查询字符串
                        $queryParams = $_GET;
                        unset($queryParams['page']);
                        $queryString = http_build_query($queryParams);
                        $queryPrefix = !empty($queryString) ? "?$queryString&" : "?";
                        
                        // 首页和上一页
                        if ($page > 1):
                        ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= $queryPrefix ?>page=1" aria-label="首页">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="<?= $queryPrefix ?>page=<?= $page - 1 ?>" aria-label="上一页">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for($i = $startPage; $i <= $endPage; $i++): ?>
                            <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                <a class="page-link" href="<?= $queryPrefix ?>page=<?= $i ?>"><?= $i ?></a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php 
                        // 下一页和末页
                        if ($page < $totalPages):
                        ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= $queryPrefix ?>page=<?= $page + 1 ?>" aria-label="下一页">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="<?= $queryPrefix ?>page=<?= $totalPages ?>" aria-label="末页">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src="../assets/js/jquery.min.js"></script>
<script src="../assets/js/bootstrap.bundle.min.js"></script>
<script src="../assets/js/fix_frontend_errors.js"></script>
<script>
$(document).ready(function() {
    // 更新父窗口中的菜单高亮状态和localStorage
    if (window.parent && window.parent.document) {
        // 更新localStorage中保存的当前页面
        window.parent.localStorage.setItem('currentPage', 'pages/operation_logs.php');
        
        // 更新父窗口中的菜单高亮状态
        const menuItems = window.parent.document.querySelectorAll('a[target="contentFrame"]');
        menuItems.forEach(item => {
            item.classList.remove('active-menu-item');
        });
        const logLink = window.parent.document.querySelector('a[data-page="pages/operation_logs.php"]');
        if (logLink) {
            logLink.classList.add('active-menu-item');
        }
    }
    
    // 修复分页链接，确保它们在iframe内正确工作
    $('a.page-link').on('click', function(e) {
        e.preventDefault();
        const href = $(this).attr('href');
        window.location.href = href;
    });
    
    // 修复搜索表单提交
    $('form').on('submit', function(e) {
        e.preventDefault();
        const searchValue = $('input[name="search"]').val();
        window.location.href = 'operation_logs.php' + (searchValue ? '?search=' + encodeURIComponent(searchValue) : '');
    });
});
</script>
</body>
</html>