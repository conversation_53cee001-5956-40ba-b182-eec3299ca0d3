<?php
require '../includes/config.php';
requireRole('admin'); // 只有管理员可以访问

// 处理解绑操作
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] == 'unbind') {
            $bindingId = $_POST['binding_id'] ?? '';
            if (empty($bindingId)) {
                throw new Exception('参数错误');
            }
            
            // 获取绑定信息
            $stmt = $pdo->prepare("
                SELECT uwb.*, u.username, wu.openid, wu.nickname
                FROM user_wechat_bindings uwb
                JOIN users u ON uwb.user_id = u.id
                JOIN wechat_users wu ON uwb.wechat_user_id = wu.id
                WHERE uwb.id = ?
            ");
            $stmt->execute([$bindingId]);
            $binding = $stmt->fetch();
            
            if (!$binding) {
                throw new Exception('绑定关系不存在');
            }
            
            // 禁用绑定关系
            $stmt = $pdo->prepare("
                UPDATE user_wechat_bindings 
                SET is_active = 0, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$bindingId]);
            
            // 删除相关token
            $stmt = $pdo->prepare("
                DELETE FROM wechat_tokens 
                WHERE user_id = ? AND wechat_user_id = ?
            ");
            $stmt->execute([$binding['user_id'], $binding['wechat_user_id']]);
            
            // 记录日志
            $stmt = $pdo->prepare("
                INSERT INTO wechat_login_logs (openid, user_id, action, ip_address, user_agent, result)
                VALUES (?, ?, 'unbind', ?, ?, 'success')
            ");
            $stmt->execute([
                $binding['openid'],
                $binding['user_id'],
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
            
            // 记录操作日志
            logAction('解绑微信账号', 'wechat_bindings', [
                'username' => $binding['username'],
                'wechat_nickname' => $binding['nickname']
            ]);
            
            header("Location: wechat_bindings.php?success=unbind");
            exit();
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// 分页逻辑
$search = isset($_GET['search']) ? "%{$_GET['search']}%" : '%';
$page = max(1, intval($_GET['page'] ?? 1));
$offset = ($page - 1) * ITEMS_PER_PAGE;

// 查询绑定关系
$sql = "SELECT SQL_CALC_FOUND_ROWS 
            uwb.id as binding_id,
            uwb.bind_type,
            uwb.bind_time,
            uwb.bind_ip,
            uwb.is_active,
            u.id as user_id,
            u.username,
            u.role,
            u.is_station_staff,
            wu.id as wechat_user_id,
            wu.openid,
            wu.nickname,
            wu.avatar_url,
            wu.city,
            wu.province,
            wu.created_at as wechat_created_at
        FROM user_wechat_bindings uwb
        JOIN users u ON uwb.user_id = u.id
        JOIN wechat_users wu ON uwb.wechat_user_id = wu.id
        WHERE (u.username LIKE ? OR wu.nickname LIKE ?)
        ORDER BY uwb.bind_time DESC
        LIMIT ? OFFSET ?";

$stmt = $pdo->prepare($sql);
$stmt->execute([$search, $search, ITEMS_PER_PAGE, $offset]);
$bindings = $stmt->fetchAll();
$total = $pdo->query("SELECT FOUND_ROWS()")->fetchColumn();

// 获取统计信息
$stats = [];
$stmt = $pdo->query("SELECT COUNT(*) FROM user_wechat_bindings WHERE is_active = 1");
$stats['active_bindings'] = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) FROM wechat_users");
$stats['total_wechat_users'] = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) FROM wechat_tokens WHERE expires_at > NOW()");
$stats['active_tokens'] = $stmt->fetchColumn();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>微信绑定管理</title>
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/apple-style.css" rel="stylesheet">
    <style>
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stats-item {
            text-align: center;
            padding: 15px;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .wechat-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .bind-type-badge {
            font-size: 0.75rem;
            padding: 2px 6px;
        }
        
        .status-active {
            color: #28a745;
        }
        
        .status-inactive {
            color: #dc3545;
        }
    </style>
    <script src="../assets/js/fix_frontend_errors.js"></script>
</head>
<body>
<div class="container-fluid">
    <?php if(isset($_GET['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php 
            if ($_GET['success'] === 'unbind') {
                echo '微信账号解绑成功！';
            }
            ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>
    
    <?php if(isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($error) ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <!-- 页面标题 -->
    <div class="page-header">
        <h3><i class="fab fa-weixin mr-2"></i>微信绑定管理</h3>
    </div>

    <!-- 统计信息 -->
    <div class="stats-card">
        <div class="row">
            <div class="col-md-4">
                <div class="stats-item">
                    <div class="stats-number"><?= $stats['active_bindings'] ?></div>
                    <div class="stats-label">活跃绑定</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-item">
                    <div class="stats-number"><?= $stats['total_wechat_users'] ?></div>
                    <div class="stats-label">微信用户</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-item">
                    <div class="stats-number"><?= $stats['active_tokens'] ?></div>
                    <div class="stats-label">在线Token</div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <i class="fas fa-link mr-2"></i>绑定关系列表
        </div>
        
        <div class="card-body py-3">
            <!-- 搜索表单和统计信息 -->
            <div class="row align-items-center mb-3">
                <div class="col-md-6">
                    <span class="text-muted">
                        共找到 <strong class="text-primary"><?= $total ?></strong> 个绑定关系
                    </span>
                </div>
                <div class="col-md-6">
                    <form class="form-inline justify-content-end" method="get">
                        <div class="input-group input-group-sm">
                            <input type="text" class="form-control" name="search" placeholder="搜索用户名或微信昵称" value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="submit" title="搜索">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 绑定关系表格 -->
            <div class="table-responsive">
                <table class="table table-bordered table-hover mb-0">
                    <thead>
                        <tr class="text-center">
                            <th style="width: 8%">序号</th>
                            <th style="width: 15%">系统用户</th>
                            <th style="width: 20%">微信信息</th>
                            <th style="width: 12%">绑定类型</th>
                            <th style="width: 15%">绑定时间</th>
                            <th style="width: 10%">状态</th>
                            <th style="width: 20%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(count($bindings) > 0): ?>
                            <?php 
                            $startNumber = ($page - 1) * ITEMS_PER_PAGE + 1;
                            foreach($bindings as $index => $binding): 
                            ?>
                                <tr>
                                    <td class="text-center"><?= $startNumber + $index ?></td>
                                    <td>
                                        <div>
                                            <i class="fas fa-user-circle mr-2"></i>
                                            <strong><?= htmlspecialchars($binding['username']) ?></strong>
                                        </div>
                                        <small class="text-muted">
                                            <?php
                                            $roleText = $binding['role'] === 'admin' ? '管理员' : 
                                                       ($binding['role'] === 'manager' ? '普通管理员' : '普通用户');
                                            echo $roleText;
                                            if ($binding['is_station_staff']) {
                                                echo ' | 一站人员';
                                            }
                                            ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if ($binding['avatar_url']): ?>
                                                <img src="<?= htmlspecialchars($binding['avatar_url']) ?>" 
                                                     class="wechat-avatar mr-2" alt="头像">
                                            <?php else: ?>
                                                <div class="wechat-avatar mr-2 bg-secondary d-flex align-items-center justify-content-center">
                                                    <i class="fab fa-weixin text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div><strong><?= htmlspecialchars($binding['nickname'] ?: '未设置昵称') ?></strong></div>
                                                <small class="text-muted">
                                                    <?= htmlspecialchars($binding['city'] ?: '') ?>
                                                    <?= htmlspecialchars($binding['province'] ?: '') ?>
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bind-type-badge <?= $binding['bind_type'] === 'manual' ? 'badge-primary' : 'badge-info' ?>">
                                            <?= $binding['bind_type'] === 'manual' ? '手动绑定' : '自动绑定' ?>
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <div><?= date('Y-m-d', strtotime($binding['bind_time'])) ?></div>
                                        <small class="text-muted"><?= date('H:i:s', strtotime($binding['bind_time'])) ?></small>
                                    </td>
                                    <td class="text-center">
                                        <?php if ($binding['is_active']): ?>
                                            <i class="fas fa-check-circle status-active" title="已激活"></i>
                                            <span class="status-active">激活</span>
                                        <?php else: ?>
                                            <i class="fas fa-times-circle status-inactive" title="已禁用"></i>
                                            <span class="status-inactive">禁用</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if ($binding['is_active']): ?>
                                            <button class="btn btn-sm btn-danger" 
                                                    onclick="confirmUnbind(<?= $binding['binding_id'] ?>, '<?= htmlspecialchars($binding['username']) ?>', '<?= htmlspecialchars($binding['nickname'] ?: '未设置昵称') ?>')">
                                                <i class="fas fa-unlink mr-1"></i>解绑
                                            </button>
                                        <?php else: ?>
                                            <span class="text-muted">已解绑</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="fas fa-info-circle mr-1 text-info"></i> 没有找到符合条件的绑定关系
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <?php if($total > ITEMS_PER_PAGE): ?>
                <nav class="mt-3">
                    <ul class="pagination">
                        <?php 
                        $totalPages = ceil($total / ITEMS_PER_PAGE);
                        $maxPagesToShow = 5;
                        $startPage = max(1, min($page - floor($maxPagesToShow / 2), $totalPages - $maxPagesToShow + 1));
                        $endPage = min($totalPages, $startPage + $maxPagesToShow - 1);
                        
                        // 构建查询字符串
                        $queryParams = $_GET;
                        unset($queryParams['page']);
                        $queryString = http_build_query($queryParams);
                        $queryPrefix = !empty($queryString) ? "?$queryString&" : "?";
                        
                        // 首页和上一页
                        if ($page > 1):
                        ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= $queryPrefix ?>page=1" aria-label="首页">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="<?= $queryPrefix ?>page=<?= $page - 1 ?>" aria-label="上一页">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for($i = $startPage; $i <= $endPage; $i++): ?>
                            <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                <a class="page-link" href="<?= $queryPrefix ?>page=<?= $i ?>"><?= $i ?></a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php 
                        // 下一页和末页
                        if ($page < $totalPages):
                        ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= $queryPrefix ?>page=<?= $page + 1 ?>" aria-label="下一页">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="<?= $queryPrefix ?>page=<?= $totalPages ?>" aria-label="末页">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- 解绑确认模态框 -->
<div class="modal fade" id="unbindModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="fas fa-unlink mr-2"></i>确认解绑</h5>
                <button type="button" class="close text-white" data-dismiss="modal">&times;</button>
            </div>
            <form id="unbindForm" method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="unbind">
                    <input type="hidden" name="binding_id" id="unbindBindingId">
                    <p>确定要解绑以下账号的微信绑定关系吗？</p>
                    <div class="alert alert-warning">
                        <strong>系统用户：</strong><span id="unbindUsername"></span><br>
                        <strong>微信昵称：</strong><span id="unbindWechatName"></span>
                    </div>
                    <p class="text-danger">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        解绑后，该微信账号将无法登录系统，需要重新绑定才能使用。
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-unlink mr-1"></i>确认解绑
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../assets/js/jquery.min.js"></script>
<script src="../assets/js/bootstrap.bundle.min.js"></script>
<script>
function confirmUnbind(bindingId, username, wechatName) {
    $('#unbindBindingId').val(bindingId);
    $('#unbindUsername').text(username);
    $('#unbindWechatName').text(wechatName);
    $('#unbindModal').modal('show');
}

$(document).ready(function() {
    // 更新父窗口中的菜单高亮状态
    if (window.parent && window.parent.document) {
        window.parent.localStorage.setItem('currentPage', 'pages/wechat_bindings.php');
        
        const menuItems = window.parent.document.querySelectorAll('a[target="contentFrame"]');
        menuItems.forEach(item => {
            item.classList.remove('active-menu-item');
        });
        const wechatLink = window.parent.document.querySelector('a[data-page="pages/wechat_bindings.php"]');
        if (wechatLink) {
            wechatLink.classList.add('active-menu-item');
        }
    }
});
</script>
</body>
</html>
