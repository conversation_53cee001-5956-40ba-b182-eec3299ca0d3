<?php
require '../includes/config.php';
requireRole('admin');

// 检查并更新 login_attempts 表结构
function updateLoginAttemptsTable($pdo) {
    try {
        // 检查 module 列是否存在
        $stmt = $pdo->query("SHOW COLUMNS FROM login_attempts LIKE 'module'");
        if ($stmt->rowCount() == 0) {
            // 添加 module 列
            $pdo->exec("ALTER TABLE login_attempts ADD COLUMN module VARCHAR(50) DEFAULT 'unknown' AFTER is_success");
            // 尝试添加索引（如果不存在的话）
            try {
                $pdo->exec("ALTER TABLE login_attempts ADD INDEX idx_username_time (username, attempt_time)");
            } catch (PDOException $e) {
                // 索引可能已存在，忽略错误
            }
            try {
                $pdo->exec("ALTER TABLE login_attempts ADD INDEX idx_ip_time (ip_address, attempt_time)");
            } catch (PDOException $e) {
                // 索引可能已存在，忽略错误
            }
        }
    } catch (PDOException $e) {
        error_log("更新 login_attempts 表结构失败: " . $e->getMessage());
    }
}

// 更新表结构
updateLoginAttemptsTable($pdo);

// 处理IP相关操作 - 将这部分移到最前面
// 解锁特定IP
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'unlock_ip') {
    $ipAddress = $_POST['ip_address'] ?? '';
    
    if (!empty($ipAddress)) {
        try {
            // 删除该IP的所有失败登录记录，从而解除锁定
            $stmt = $pdo->prepare("DELETE FROM login_attempts WHERE ip_address = ? AND is_success = 0");
            $stmt->execute([$ipAddress]);

            // 记录操作日志
            logAction('解锁IP', 'security', ['ip_address' => $ipAddress]);

            header("Location: users.php?success=ip_unlocked");
            exit();
        } catch(PDOException $e) {
            die("<script>alert('解锁IP失败: " . $e->getMessage() . "');history.back();</script>");
        }
    }
}

// 清除所有IP记录
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'clear_all_ips') {
    try {
        // 直接执行TRUNCATE语句清空表
        $pdo->exec("TRUNCATE TABLE login_attempts");
        
        // 记录操作日志
        logAction('清除所有IP记录', 'security', []);
        
        header("Location: users.php?success=all_ips_cleared");
        exit();
    } catch(PDOException $e) {
        die("<script>alert('清除IP记录失败: " . $e->getMessage() . "');history.back();</script>");
    }
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // 检查是否是常规表单提交
        if (isset($_POST['username']) && isset($_POST['role']) && isset($_POST['password'])) {
            // 常规表单提交处理
            $username = trim($_POST['username']);
            $role = $_POST['role'];
            $password = $_POST['password'];
            
            // 验证
            if (strlen($username) < 2) {
                die("<script>alert('用户名长度必须至少为2位');history.back();</script>");
            }
            
            if (strlen($password) < 6) {
                die("<script>alert('密码长度必须至少为6位');history.back();</script>");
            }
            
            // 检查用户名唯一性
            $check = $pdo->prepare("SELECT id FROM users WHERE username = ?");
            $check->execute([$username]);
            if($check->fetch()) {
                die("<script>alert('用户名已存在');history.back();</script>");
            }
            
            // 获取一站人员身份设置
            $isStationStaff = isset($_POST['is_station_staff']) ? 1 : 0;

            // 创建用户
            $stmt = $pdo->prepare("INSERT INTO users (username, role, password, is_station_staff) VALUES (?,?,?,?)");
            $stmt->execute([
                $username,
                $role,
                password_hash($password, PASSWORD_DEFAULT),
                $isStationStaff
            ]);
            
            // 记录操作日志
            logAction('创建新用户', 'users', ['username' => $username, 'role' => $role]);
            
            // 重定向回用户列表
            header("Location: users.php?success=1");
            exit();
        }
        
        // JSON API 处理
        $data = json_decode(file_get_contents('php://input'), true);

        // 修改用户角色逻辑
        if (isset($data['change_role'])) {
            $required = ['user_id' => '用户ID', 'role' => '角色'];
            foreach ($required as $field => $name) {
                if (empty($data[$field])) die(json_encode(['status'=>'error','message'=>"$name 不能为空"]));
            }

            $allowedRoles = ['admin', 'manager', 'user'];
            $newRole = $data['role'];
            $userId = $data['user_id'];

            if (!in_array($newRole, $allowedRoles)) {
                die(json_encode(['status'=>'error','message'=>'无效的角色']));
            }

            if ($userId == $_SESSION['user']['id']) {
                die(json_encode(['status'=>'error','message'=>'不能修改自己的角色']));
            }

            $stmt = $pdo->prepare("UPDATE users SET role = ? WHERE id = ?");
            $stmt->execute([$newRole, $userId]);

            // 记录操作日志
            logAction('修改用户角色', 'users', ['id' => $userId, 'new_role' => $newRole]);

            echo json_encode(['status'=>'success', 'message'=>'角色修改成功']);
            exit();
        }

        // 切换一站人员身份逻辑
        if (isset($data['toggle_station'])) {
            $required = ['user_id' => '用户ID'];
            foreach ($required as $field => $name) {
                if (empty($data[$field])) die(json_encode(['status'=>'error','message'=>"$name 不能为空"]));
            }

            $userId = $data['user_id'];

            // 获取当前一站人员状态
            $stmt = $pdo->prepare("SELECT is_station_staff FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();

            if (!$user) {
                die(json_encode(['status'=>'error','message'=>'用户不存在']));
            }

            $newStatus = $user['is_station_staff'] ? 0 : 1;
            $stmt = $pdo->prepare("UPDATE users SET is_station_staff = ? WHERE id = ?");
            $stmt->execute([$newStatus, $userId]);

            $action = $newStatus ? '设置一站人员身份' : '取消一站人员身份';
            logAction($action, 'users', ['id' => $userId, 'is_station_staff' => $newStatus]);

            echo json_encode(['status'=>'success', 'message'=>$action.'成功', 'new_status' => $newStatus]);
            exit();
        }

        // 删除用户逻辑
        if (isset($data['delete_user'])) {
            $required = ['user_id' => '用户ID'];
            foreach ($required as $field => $name) {
                if (empty($data[$field])) die(json_encode(['status'=>'error','message'=>"$name 不能为空"]));
            }

            $userId = $data['user_id'];

            if ($userId == $_SESSION['user']['id']) {
                die(json_encode(['status'=>'error','message'=>'不能删除自己的账号']));
            }

            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            $stmt->execute([$userId]);

            // 记录操作日志
            logAction('删除用户', 'users', ['id' => $userId]);

            echo json_encode(['status'=>'success', 'message'=>'用户删除成功']);
            exit();
        }

        // 修改密码逻辑
        if (isset($data['change_password'])) {
            $required = ['user_id' => '用户ID', 'password' => '密码'];
            foreach ($required as $field => $name) {
                if (empty($data[$field])) die(json_encode(['status'=>'error','message'=>"$name 不能为空"]));
            }

            $stmt = $pdo->prepare("UPDATE users SET password=? WHERE id=?");
            $stmt->execute([
                password_hash($data['password'], PASSWORD_DEFAULT),
                $data['user_id']
            ]);
            
            // 记录操作日志
            logAction('更新用户密码', 'users', ['user_id' => $data['user_id']]);
            
            echo json_encode(['status'=>'success', 'message'=>'密码修改成功']);
            exit();
        }

        // 新建用户逻辑
        $required = ['username' => '用户名', 'role' => '角色', 'password' => '密码'];
        foreach ($required as $field => $name) {
            if (empty($data[$field])) die(json_encode(['status'=>'error','message'=>"$name 不能为空"]));
        }

        // 检查用户名唯一性
        $check = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $check->execute([$data['username']]);
        if($check->fetch()) die(json_encode(['status'=>'error','message'=>'用户名已存在']));

        // 获取一站人员身份设置
        $isStationStaff = !empty($data['is_station_staff']) ? 1 : 0;

        // 创建用户
        $stmt = $pdo->prepare("INSERT INTO users (username, role, password, is_station_staff) VALUES (?,?,?,?)");
        $stmt->execute([
            $data['username'],
            $data['role'],
            password_hash($data['password'], PASSWORD_DEFAULT),
            $isStationStaff
        ]);
        
        // 记录操作日志
        logAction('创建新用户', 'users', ['username' => $data['username'], 'role' => $data['role']]);
        
        echo json_encode(['status'=>'success', 'message'=>'用户创建成功']);
        exit();

    } catch (PDOException $e) {
        die(json_encode(['status'=>'error','message'=>$e->getMessage()]));
    } catch (Exception $e) {
        die(json_encode(['status'=>'error','message'=>$e->getMessage()]));
    }
}

// 以下GET请求处理已改为AJAX方式，保留作为备用
/*
// 删除用户（防止删除自己）
if (isset($_GET['delete'])) {
    if ($_GET['delete'] == $_SESSION['user']['id']) {
        die("<script>alert('不能删除自己的账号！');history.back();</script>");
    }
    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
    $stmt->execute([$_GET['delete']]);
    logAction('删除用户', 'users', ['id' => $_GET['delete']]);
    header("Location: users.php");
    exit();
}

// 处理修改角色请求
if (isset($_GET['change_role']) && isset($_GET['role']) && isset($_GET['user_id'])) {
    $allowedRoles = ['admin', 'manager', 'user'];
    $newRole = $_GET['role'];
    $userId = $_GET['user_id'];

    if (in_array($newRole, $allowedRoles) && $userId != $_SESSION['user']['id']) {
        $stmt = $pdo->prepare("UPDATE users SET role = ? WHERE id = ?");
        $stmt->execute([$newRole, $userId]);
        logAction('修改用户角色', 'users', ['id' => $userId, 'new_role' => $newRole]);
        header("Location: users.php");
        exit();
    } else {
        die("<script>alert('无效的角色或不能修改自己的角色！');history.back();</script>");
    }
}

// 处理一站人员身份切换请求
if (isset($_GET['toggle_station']) && isset($_GET['user_id'])) {
    $userId = $_GET['user_id'];

    // 获取当前一站人员状态
    $stmt = $pdo->prepare("SELECT is_station_staff FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();

    if ($user) {
        $newStatus = $user['is_station_staff'] ? 0 : 1;
        $stmt = $pdo->prepare("UPDATE users SET is_station_staff = ? WHERE id = ?");
        $stmt->execute([$newStatus, $userId]);

        $action = $newStatus ? '设置一站人员身份' : '取消一站人员身份';
        logAction($action, 'users', ['id' => $userId, 'is_station_staff' => $newStatus]);

        header("Location: users.php");
        exit();
    } else {
        die("<script>alert('用户不存在！');history.back();</script>");
    }
}
*/

// 分页逻辑
$search = isset($_GET['search']) ? "%{$_GET['search']}%" : '%';
$page = max(1, intval($_GET['page'] ?? 1));
$offset = ($page - 1) * ITEMS_PER_PAGE;

$sql = "SELECT SQL_CALC_FOUND_ROWS * FROM users
        WHERE username LIKE ?
        ORDER BY id ASC
        LIMIT ? OFFSET ?";
$stmt = $pdo->prepare($sql);
$stmt->execute([$search, ITEMS_PER_PAGE, $offset]);
$users = $stmt->fetchAll();
$total = $pdo->query("SELECT FOUND_ROWS()")->fetchColumn();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>用户管理</title>
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link href="../assets/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/apple-style.css" rel="stylesheet">
    <style>
        /* 用户管理页面特定样式 */
        
        .badge-role {
            padding: 4px 8px;
            font-size: 11px;
            font-weight: 500;
            border-radius: 6px;
            letter-spacing: 0.06em;
        }

        .badge-admin {
            background-color: rgba(255, 59, 48, 0.1);
            color: #ff3b30;
        }

        .badge-manager {
            background-color: rgba(255, 149, 0, 0.1);
            color: #ff9500;
        }

        .badge-user {
            background-color: rgba(52, 199, 89, 0.1);
            color: #34c759;
        }

        .badge-station {
            background-color: rgba(0, 122, 255, 0.1);
            color: #007aff;
        }

        .btn-action {
            margin-right: 8px;
        }
        
        .alert-float {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            color: white;
            border-radius: 10px 10px 0 0;
        }
        
        .modal-content {
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .close {
            color: white;
            text-shadow: none;
            opacity: 0.8;
        }
        
        .close:hover {
            color: white;
            opacity: 1;
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            color: white;
            border: none;
        }
        
        .btn-gradient:hover {
            background: linear-gradient(135deg, #5d7ec9 0%, #243a6a 100%);
            color: white;
        }
        
        .role-description {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }
        
        .role-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .role-icon {
            width: 24px;
            text-align: center;
            margin-right: 10px;
        }

        .gap-1 > * {
            margin-right: 4px !important;
        }

        .gap-1 > *:last-child {
            margin-right: 0 !important;
        }

        /* 优化页面顶部间距 */
        .page-header {
            margin-bottom: 1.5rem !important;
        }

        .card-header {
            padding: 0.75rem 1.25rem !important;
            background-color: #f8f9fa !important;
            border-bottom: 1px solid #dee2e6 !important;
        }

        .card-body.py-3 {
            padding-top: 1rem !important;
            padding-bottom: 1rem !important;
        }

        /* 搜索区域优化 */
        .input-group-sm .form-control {
            font-size: 0.875rem;
            border-radius: 6px;
        }

        .input-group-sm .btn {
            border-radius: 0 6px 6px 0;
        }

        /* 统计信息样式 */
        .text-primary {
            color: #007aff !important;
        }

        /* 表格优化 */
        .table {
            margin-bottom: 0 !important;
        }

        .table th {
            padding: 0.5rem 0.75rem !important;
            font-size: 0.875rem;
            font-weight: 600;
            background-color: #f8f9fa !important;
            border-bottom: 2px solid #dee2e6 !important;
        }

        .table td {
            padding: 0.5rem 0.75rem !important;
            font-size: 0.875rem;
            vertical-align: middle !important;
        }

        /* 减少搜索区域的底部间距 */
        .row.align-items-center.mb-3 {
            margin-bottom: 1rem !important;
        }

        /* 容器间距优化 */
        .container-fluid {
            padding-top: 1rem !important;
        }
    </style>
<script src="../assets/js/fix_frontend_errors.js"></script></head>
<body>
<div class="container-fluid">
    <?php if(isset($_GET['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php 
            $success = $_GET['success'];
            if ($success === '1') {
                echo '用户创建成功！';
            } elseif ($success === 'ip_unlocked') {
                echo 'IP解锁成功！';
            } elseif ($success === 'all_ips_cleared') {
                echo '所有IP记录已清除！';
            }
            ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>
    <!-- 页面标题 -->
    <div class="page-header">
        <h3><i class="fas fa-users mr-2"></i>用户管理</h3>
        <button class="btn btn-primary" data-toggle="modal" data-target="#newUserModal">
            <i class="fas fa-plus mr-1"></i> 新建用户
        </button>
    </div>

    <div class="card">
        <div class="card-header">
            <i class="fas fa-list mr-2"></i>用户列表
        </div>
        
        <div class="card-body py-3">
            <!-- 搜索表单和统计信息 -->
            <div class="row align-items-center mb-3">
                <div class="col-md-6">
                    <span class="text-muted">
                        共找到 <strong class="text-primary"><?= $total ?></strong> 个用户
                    </span>
                </div>
                <div class="col-md-6">
                    <form class="form-inline justify-content-end" method="get">
                        <div class="input-group input-group-sm">
                            <input type="text" class="form-control" name="search" placeholder="输入用户名搜索" value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="submit" title="搜索">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 用户表格 -->
            <div class="table-responsive">
                <table class="table table-bordered table-hover mb-0">
                    <thead>
                        <tr class="text-center">
                            <th style="width: 8%">序号</th>
                            <th style="width: 25%">用户名</th>
                            <th style="width: 25%">角色</th>
                            <th style="width: 42%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(count($users) > 0): ?>
                            <?php 
                            // 计算序号起始值
                            $startNumber = ($page - 1) * ITEMS_PER_PAGE + 1;
                            foreach($users as $index => $user): 
                            ?>
                                <tr>
                                    <td class="text-center"><?= $startNumber + $index ?></td>
                                    <td>
                                        <i class="fas fa-user-circle mr-2"></i>
                                        <?= htmlspecialchars($user['username'] ?? '') ?>
                                    </td>
                                    <td class="text-center">
                                        <?php
                                        $roleBadgeClass = 'badge-user';
                                        $roleText = '普通用户';
                                        $roleIcon = 'fa-user';
                                        $roleDescription = '无后台登录权限';
                                        
                                        if ($user['role'] === 'admin') {
                                            $roleBadgeClass = 'badge-admin';
                                            $roleText = '管理员';
                                            $roleIcon = 'fa-user-shield';
                                            $roleDescription = '拥有所有权限';
                                        } elseif ($user['role'] === 'manager') {
                                            $roleBadgeClass = 'badge-manager';
                                            $roleText = '普通管理员';
                                            $roleIcon = 'fa-user-cog';
                                            $roleDescription = '除用户管理外的所有权限';
                                        }
                                        ?>
                                        <div class="d-flex flex-wrap justify-content-center align-items-center">
                                            <span class="badge badge-role <?= $roleBadgeClass ?> mb-1 mr-1"
                                                  data-toggle="tooltip" title="<?= $roleDescription ?>">
                                                <i class="fas <?= $roleIcon ?> mr-1"></i>
                                                <?= $roleText ?>
                                            </span>
                                            <?php if ($user['is_station_staff'] == 1): ?>
                                                <span class="badge badge-station mb-1"
                                                      data-toggle="tooltip" title="拥有一站人员身份，可查看津贴信息">
                                                    <i class="fas fa-user-tie mr-1"></i>
                                                    一站人员
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="d-flex flex-wrap justify-content-center align-items-center gap-1">
                                            <button class="btn btn-sm btn-warning mb-1"
                                                    data-id="<?= $user['id'] ?>"
                                                    data-username="<?= htmlspecialchars($user['username']) ?>"
                                                    onclick="editPassword(this)">
                                                <i class="fas fa-key"></i> 密码
                                            </button>

                                            <?php if($user['id'] != $_SESSION['user']['id']): ?>
                                                <div class="btn-group mb-1">
                                                    <button type="button" class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown">
                                                        <i class="fas fa-user-tag"></i> 角色
                                                    </button>
                                                    <div class="dropdown-menu">
                                                        <a class="dropdown-item <?= $user['role'] == 'admin' ? 'active' : '' ?>"
                                                           href="javascript:void(0)"
                                                           data-user-id="<?= $user['id'] ?>"
                                                           data-username="<?= htmlspecialchars($user['username']) ?>"
                                                           data-role="admin"
                                                           onclick="changeUserRole(this)">
                                                            <i class="fas fa-user-shield mr-2"></i>管理员
                                                        </a>
                                                        <a class="dropdown-item <?= $user['role'] == 'manager' ? 'active' : '' ?>"
                                                           href="javascript:void(0)"
                                                           data-user-id="<?= $user['id'] ?>"
                                                           data-username="<?= htmlspecialchars($user['username']) ?>"
                                                           data-role="manager"
                                                           onclick="changeUserRole(this)">
                                                            <i class="fas fa-user-cog mr-2"></i>普通管理员
                                                        </a>
                                                        <a class="dropdown-item <?= $user['role'] == 'user' ? 'active' : '' ?>"
                                                           href="javascript:void(0)"
                                                           data-user-id="<?= $user['id'] ?>"
                                                           data-username="<?= htmlspecialchars($user['username']) ?>"
                                                           data-role="user"
                                                           onclick="changeUserRole(this)">
                                                            <i class="fas fa-user mr-2"></i>普通用户
                                                        </a>
                                                        <div class="dropdown-divider"></div>
                                                        <a class="dropdown-item"
                                                           href="javascript:void(0)"
                                                           data-user-id="<?= $user['id'] ?>"
                                                           data-username="<?= htmlspecialchars($user['username']) ?>"
                                                           data-current-status="<?= $user['is_station_staff'] ?>"
                                                           onclick="toggleStationStaff(this)">
                                                            <i class="fas fa-user-tie mr-2 <?= $user['is_station_staff'] ? 'text-danger' : 'text-success' ?>"></i>
                                                            <?= $user['is_station_staff'] ? '取消一站人员身份' : '设置一站人员身份' ?>
                                                        </a>
                                                    </div>
                                                </div>

                                                <button class="btn btn-sm btn-danger mb-1"
                                                        data-user-id="<?= $user['id'] ?>"
                                                        data-username="<?= htmlspecialchars($user['username']) ?>"
                                                        onclick="deleteUser(this)">
                                                    <i class="fas fa-trash"></i> 删除
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-sm btn-secondary mb-1" disabled title="不能删除当前登录的用户">
                                                    <i class="fas fa-trash"></i> 删除
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="4" class="text-center py-4">
                                    <i class="fas fa-info-circle mr-1 text-info"></i> 没有找到符合条件的用户
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <?php if($total > ITEMS_PER_PAGE): ?>
                <nav>
                    <ul class="pagination">
                        <?php 
                        $totalPages = ceil($total / ITEMS_PER_PAGE);
                        $maxPagesToShow = 5;
                        $startPage = max(1, min($page - floor($maxPagesToShow / 2), $totalPages - $maxPagesToShow + 1));
                        $endPage = min($totalPages, $startPage + $maxPagesToShow - 1);
                        
                        // 构建查询字符串
                        $queryParams = $_GET;
                        unset($queryParams['page']);
                        $queryString = http_build_query($queryParams);
                        $queryPrefix = !empty($queryString) ? "?$queryString&" : "?";
                        
                        // 首页和上一页
                        if ($page > 1):
                        ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= $queryPrefix ?>page=1" aria-label="首页">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="<?= $queryPrefix ?>page=<?= $page - 1 ?>" aria-label="上一页">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for($i = $startPage; $i <= $endPage; $i++): ?>
                            <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                <a class="page-link" href="<?= $queryPrefix ?>page=<?= $i ?>"><?= $i ?></a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php 
                        // 下一页和末页
                        if ($page < $totalPages):
                        ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= $queryPrefix ?>page=<?= $page + 1 ?>" aria-label="下一页">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="<?= $queryPrefix ?>page=<?= $totalPages ?>" aria-label="末页">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>

    <!-- IP锁定记录卡片 -->
    <div class="card mt-4">
        <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-shield-alt mr-2"></i>IP锁定记录</h5>
            <button class="btn btn-outline-dark btn-sm" onclick="refreshIpRecords()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
        </div>
        <div class="card-body">
            <?php
            // 获取IP锁定记录和最近登录尝试记录
            try {
                // 查询一小时内尝试次数超过3次的IP记录（锁定IP）
                $lockedIpStmt = $pdo->query("SELECT ip_address, COUNT(*) as attempts, MAX(attempt_time) as last_attempt
                                    FROM login_attempts
                                    WHERE is_success = 0
                                    AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
                                    GROUP BY ip_address
                                    HAVING COUNT(*) >= 3
                                    ORDER BY attempts DESC");
                $lockedIps = $lockedIpStmt->fetchAll(PDO::FETCH_ASSOC);

                // 查询最近5条登录尝试记录
                try {
                    // 检查 module 列是否存在
                    $checkColumnStmt = $pdo->query("SHOW COLUMNS FROM login_attempts LIKE 'module'");
                    $hasModuleColumn = $checkColumnStmt->rowCount() > 0;

                    if ($hasModuleColumn) {
                        $recentAttemptsStmt = $pdo->query("SELECT
                            username,
                            ip_address,
                            attempt_time,
                            is_success,
                            COALESCE(module, 'unknown') as module,
                            CASE
                                WHEN COALESCE(module, 'unknown') = 'tag_query' THEN '仪表位号及阀门编号查询'
                                WHEN COALESCE(module, 'unknown') = 'jintie_query' THEN '一站津贴查询'
                                WHEN COALESCE(module, 'unknown') = 'shift_schedule' THEN '倒班时间查询'
                                WHEN COALESCE(module, 'unknown') = 'admin_backend' THEN '后台管理系统'
                                ELSE '未知模块'
                            END as module_name
                        FROM login_attempts
                        ORDER BY attempt_time DESC
                        LIMIT 5");
                    } else {
                        $recentAttemptsStmt = $pdo->query("SELECT
                            username,
                            ip_address,
                            attempt_time,
                            is_success,
                            'unknown' as module,
                            '未知模块' as module_name
                        FROM login_attempts
                        ORDER BY attempt_time DESC
                        LIMIT 5");
                    }

                    $recentAttempts = $recentAttemptsStmt->fetchAll(PDO::FETCH_ASSOC);
                } catch (PDOException $e) {
                    error_log("查询登录尝试记录失败: " . $e->getMessage());
                    $recentAttempts = [];
                }

                $totalIpStmt = $pdo->query("SELECT COUNT(*) FROM login_attempts");
                $totalIpRecords = $totalIpStmt->fetchColumn();
            } catch(PDOException $e) {
                $lockedIps = [];
                $recentAttempts = [];
                $totalIpRecords = 0;
                echo '<div class="alert alert-danger">获取IP记录失败: ' . $e->getMessage() . '</div>';
            }
            ?>
            
            <div class="d-flex justify-content-between mb-3">
                <div>
                    <span class="text-muted">系统中共有 <?= $totalIpRecords ?> 条登录尝试记录</span>
                </div>
                <div>
                    <button class="btn btn-danger btn-sm" onclick="clearAllIpRecords()">
                        <i class="fas fa-trash-alt mr-1"></i>清除所有记录
                    </button>
                </div>
            </div>

            <!-- 最近登录尝试记录 -->
            <div class="mb-4">
                <h6 class="text-primary mb-3"><i class="fas fa-clock mr-2"></i>最近5条登录尝试记录</h6>
                <?php if (count($recentAttempts) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead class="thead-light">
                            <tr>
                                <th>用户名</th>
                                <th>IP地址</th>
                                <th>登录模块</th>
                                <th>尝试时间</th>
                                <th>结果</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($recentAttempts as $attempt): ?>
                            <tr>
                                <td><?= htmlspecialchars($attempt['username']) ?></td>
                                <td>
                                    <code><?= htmlspecialchars($attempt['ip_address']) ?></code>
                                </td>
                                <td>
                                    <span class="badge badge-info"><?= htmlspecialchars($attempt['module_name']) ?></span>
                                </td>
                                <td><?= $attempt['attempt_time'] ?></td>
                                <td>
                                    <?php if ($attempt['is_success']): ?>
                                        <span class="badge badge-success">成功</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">失败</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle mr-2"></i>暂无登录尝试记录
                </div>
                <?php endif; ?>
            </div>

            <!-- 被锁定的IP地址 -->
            <h6 class="text-danger mb-3"><i class="fas fa-ban mr-2"></i>被锁定的IP地址（1小时内失败3次以上）</h6>
            
            <?php if (count($lockedIps) > 0): ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>IP地址</th>
                            <th>尝试次数</th>
                            <th>最后尝试时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($lockedIps as $ip): ?>
                        <tr>
                            <td>
                                <code class="text-danger"><?= htmlspecialchars($ip['ip_address']) ?></code>
                            </td>
                            <td>
                                <span class="badge badge-<?= $ip['attempts'] >= 5 ? 'danger' : 'warning' ?>">
                                    <?= $ip['attempts'] ?> 次
                                </span>
                            </td>
                            <td>
                                <small><?= $ip['last_attempt'] ?></small>
                            </td>
                            <td>
                                <?php if ($ip['attempts'] >= 5): ?>
                                    <span class="badge badge-danger">已锁定</span>
                                <?php else: ?>
                                    <span class="badge badge-warning">警告</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-success"
                                        onclick="unlockIp('<?= htmlspecialchars($ip['ip_address']) ?>')">
                                    <i class="fas fa-unlock mr-1"></i>解除锁定
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="alert alert-success">
                <i class="fas fa-shield-alt mr-2"></i>目前没有被锁定的IP地址，系统安全状态良好
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- 浮动提示框 -->
<div class="alert alert-float" id="alertBox" role="alert"></div>

<!-- 修改密码模态框 -->
<div class="modal fade" id="passwordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-key mr-2"></i>修改密码</h5>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <form id="passwordForm">
                <div class="modal-body">
                    <input type="hidden" id="userId" name="user_id">
                    <p>正在为用户 <strong id="usernamePlaceholder"></strong> 修改密码</p>
                    <div class="form-group">
                        <label for="password">新密码</label>
                        <div class="input-group">
                            <input type="password" id="password" name="password" class="form-control" required>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <small class="form-text text-muted">密码长度至少6位，建议使用字母、数字和特殊字符的组合</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-gradient">
                        <i class="fas fa-save mr-1"></i> 保存修改
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 新建用户模态框 -->
<div class="modal fade" id="newUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-user-plus mr-2"></i>新建用户</h5>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <form id="newUserForm" method="post" action="users.php">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" class="form-control" required>
                        <small class="form-text text-muted">用户名必须唯一，最少2个汉字，建议使用字母、数字的组合</small>
                    </div>
                    <div class="form-group">
                        <label for="role">角色</label>
                        <select id="role" name="role" class="form-control" required>
                            <option value="admin">管理员</option>
                            <option value="manager">普通管理员</option>
                            <option value="user" selected>普通用户</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is_station_staff" name="is_station_staff" value="1">
                            <label class="form-check-label" for="is_station_staff">
                                <i class="fas fa-user-tie text-info mr-2"></i>
                                <strong>一站人员身份</strong>
                                <small class="text-muted d-block">可与其他角色同时拥有，用于津贴查询权限</small>
                            </label>
                        </div>
                        
                        <div class="role-description mt-3">
                            <div class="role-item">
                                <div class="role-icon"><i class="fas fa-user-shield text-danger"></i></div>
                                <div><strong>管理员：</strong>拥有所有权限，包括用户管理</div>
                            </div>
                            <div class="role-item">
                                <div class="role-icon"><i class="fas fa-user-cog text-warning"></i></div>
                                <div><strong>普通管理员：</strong>拥有除用户管理外的所有权限，可以修改自己的密码</div>
                            </div>
                            <div class="role-item">
                                <div class="role-icon"><i class="fas fa-user text-success"></i></div>
                                <div><strong>普通用户：</strong>无后台登录权限</div>
                            </div>
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-info-circle mr-2"></i>
                                <strong>一站人员身份：</strong>独立于角色的特殊身份标识，拥有此身份的用户可以使用小程序查询津贴信息。可与任何角色同时拥有。
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="newPassword">密码</label>
                        <div class="input-group">
                            <input type="password" id="newPassword" name="password" class="form-control" required>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" id="toggleNewPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <small class="form-text text-muted">密码长度至少6位，建议使用字母、数字和特殊字符的组合</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-gradient">
                        <i class="fas fa-save mr-1"></i> 创建用户
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../assets/js/jquery.min.js"></script>
<script src="../assets/js/bootstrap.bundle.min.js"></script>
<script>
// 全局显示提示框函数
function showAlert(message, type = 'success') {
    const alertBox = $('#alertBox');
    alertBox.removeClass('alert-success alert-danger alert-warning')
            .addClass('alert-' + type)
            .html(message)
            .fadeIn();

    setTimeout(function() {
        alertBox.fadeOut();
    }, 3000);
}
</script>
<script>
$(document).ready(function() {
    // 检查是否有成功消息
    <?php if(isset($_GET['success'])): ?>
    showAlertInternal('<i class="fas fa-check-circle mr-1"></i> 用户创建成功', 'success');
    <?php endif; ?>
    
    // 初始化工具提示
    $('[data-toggle="tooltip"]').tooltip();
    
    // 更新父窗口中的菜单高亮状态和localStorage
    if (window.parent && window.parent.document) {
        // 更新localStorage中保存的当前页面
        window.parent.localStorage.setItem('currentPage', 'pages/users.php');

        // 更新父窗口中的菜单高亮状态
        const menuItems = window.parent.document.querySelectorAll('a[target="contentFrame"]');
        menuItems.forEach(item => {
            item.classList.remove('active-menu-item');
        });
        const usersLink = window.parent.document.querySelector('a[data-page="pages/users.php"]');
        if (usersLink) {
            usersLink.classList.add('active-menu-item');
        }
    }
    
    // 显示提示框（内部使用）
    function showAlertInternal(message, type = 'success') {
        const alertBox = $('#alertBox');
        alertBox.removeClass('alert-success alert-danger alert-warning')
                .addClass('alert-' + type)
                .html(message)
                .fadeIn();

        setTimeout(function() {
            alertBox.fadeOut();
        }, 3000);
    }
    
    // 修改密码按钮点击事件
    $('.edit-pwd-btn').click(function() {
        const userId = $(this).data('id');
        const username = $(this).data('username');

        $('#userId').val(userId);
        $('#usernamePlaceholder').text(username);
        $('#password').val('');

        $('#passwordModal').modal('show');
    });
    
    // 删除用户按钮点击事件（已改为AJAX方式，这个事件处理器可以删除）
    
    // 修改密码表单提交
    $('#passwordForm').submit(function(e) {
        e.preventDefault();
        
        const userId = $('#userId').val();
        const password = $('#password').val();
        
        if (password.length < 6) {
            showAlertInternal('<i class="fas fa-exclamation-triangle mr-1"></i> 密码长度必须至少为6位', 'warning');
            return;
        }
        
        $.ajax({
            url: 'users.php',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                change_password: true,
                user_id: userId,
                password: password
            }),
            success: function(response) {
                try {
                    const result = typeof response === 'string' ? JSON.parse(response) : response;
                    
                    if (result.status === 'success') {
                        $('#passwordModal').modal('hide');
                        showAlertInternal('<i class="fas fa-check-circle mr-1"></i> ' + (result.message || '密码修改成功'), 'success');
                    } else {
                        showAlertInternal('<i class="fas fa-exclamation-circle mr-1"></i> ' + (result.message || '操作失败'), 'danger');
                    }
                } catch (e) {
                    showAlertInternal('<i class="fas fa-exclamation-circle mr-1"></i> 服务器响应格式错误', 'danger');
                }
            },
            error: function(xhr, status, error) {
                showAlertInternal('<i class="fas fa-exclamation-circle mr-1"></i> 服务器错误，请稍后重试', 'danger');
            }
        });
    });
    
    // 新建用户表单提交 - 使用AJAX方式
    $('#newUserForm').submit(function(e) {
        // 如果是在iframe中，使用AJAX提交
        if (window.parent && window.parent !== window) {
            e.preventDefault();
            
            const username = $('#username').val();
            const role = $('#role').val();
            const password = $('#newPassword').val();
            const isStationStaff = $('#is_station_staff').is(':checked');

            if (username.length < 2) {
                showAlertInternal('<i class="fas fa-exclamation-triangle mr-1"></i> 用户名长度必须至少为2位', 'warning');
                return false;
            }

            if (password.length < 6) {
                showAlertInternal('<i class="fas fa-exclamation-triangle mr-1"></i> 密码长度必须至少为6位', 'warning');
                return false;
            }

            $.ajax({
                url: 'users.php',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    username: username,
                    role: role,
                    password: password,
                    is_station_staff: isStationStaff
                }),
                success: function(response) {
                    try {
                        const result = typeof response === 'string' ? JSON.parse(response) : response;
                        
                        if (result.status === 'success') {
                            $('#newUserModal').modal('hide');
                            showAlertInternal('<i class="fas fa-check-circle mr-1"></i> ' + (result.message || '用户创建成功'), 'success');

                            // 延迟刷新页面，让用户看到成功消息
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            showAlertInternal('<i class="fas fa-exclamation-circle mr-1"></i> ' + (result.message || '操作失败'), 'danger');
                        }
                    } catch (e) {
                        showAlertInternal('<i class="fas fa-exclamation-circle mr-1"></i> 服务器响应格式错误', 'danger');
                    }
                },
                error: function(xhr, status, error) {
                    showAlertInternal('<i class="fas fa-exclamation-circle mr-1"></i> 服务器错误，请稍后重试', 'danger');
                }
            });
            
            return false;
        }
        // 如果是直接访问，使用常规表单提交
        return true;
    });
    
    // 密码显示/隐藏切换
    $('#togglePassword').click(function() {
        const passwordField = $('#password');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // 新密码显示/隐藏切换
    $('#toggleNewPassword').click(function() {
        const passwordField = $('#newPassword');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // 模态框关闭时重置表单
    $('#passwordModal, #newUserModal').on('hidden.bs.modal', function() {
        $(this).find('form')[0].reset();
    });
});

// IP管理相关JavaScript函数
function unlockIp(ipAddress) {
    if (confirm('确定要解锁IP ' + ipAddress + ' 吗？')) {
        // 使用隐藏表单提交
        document.getElementById('unlockIpAddress').value = ipAddress;
        document.getElementById('unlockIpForm').submit();
    }
}

function clearAllIpRecords() {
    if (confirm('确定要清除所有IP记录吗？此操作不可恢复！')) {
        document.getElementById('clearAllIpForm').submit();
    }
}

function refreshIpRecords() {
    window.location.reload();
}

// 修改密码函数
function editPassword(button) {
    const userId = $(button).data('id');
    const username = $(button).data('username');

    $('#userId').val(userId);
    $('#usernamePlaceholder').text(username);
    $('#password').val('');

    $('#passwordModal').modal('show');
}

// 修改用户角色函数
function changeUserRole(element) {
    const userId = $(element).data('user-id');
    const username = $(element).data('username');
    const newRole = $(element).data('role');

    const roleNames = {
        'admin': '管理员',
        'manager': '普通管理员',
        'user': '普通用户'
    };

    if (!confirm('确定要将用户 ' + username + ' 的角色修改为' + roleNames[newRole] + '吗？')) {
        return;
    }

    $.ajax({
        url: 'users.php',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            change_role: true,
            user_id: userId,
            role: newRole
        }),
        success: function(response) {
            try {
                const result = typeof response === 'string' ? JSON.parse(response) : response;

                if (result.status === 'success') {
                    showAlert('<i class="fas fa-check-circle mr-1"></i> ' + (result.message || '角色修改成功'), 'success');

                    // 延迟刷新页面，让用户看到成功消息
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    showAlert('<i class="fas fa-exclamation-circle mr-1"></i> ' + (result.message || '操作失败'), 'danger');
                }
            } catch (e) {
                showAlert('<i class="fas fa-exclamation-circle mr-1"></i> 服务器响应格式错误', 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('<i class="fas fa-exclamation-circle mr-1"></i> 服务器错误，请稍后重试', 'danger');
        }
    });
}

// 切换一站人员身份函数
function toggleStationStaff(element) {
    const userId = $(element).data('user-id');
    const username = $(element).data('username');
    const currentStatus = $(element).data('current-status');

    const action = currentStatus ? '取消' : '设置';

    if (!confirm('确定要' + action + '用户 ' + username + ' 的一站人员身份吗？')) {
        return;
    }

    $.ajax({
        url: 'users.php',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            toggle_station: true,
            user_id: userId
        }),
        success: function(response) {
            try {
                const result = typeof response === 'string' ? JSON.parse(response) : response;

                if (result.status === 'success') {
                    showAlert('<i class="fas fa-check-circle mr-1"></i> ' + (result.message || '操作成功'), 'success');

                    // 延迟刷新页面，让用户看到成功消息
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    showAlert('<i class="fas fa-exclamation-circle mr-1"></i> ' + (result.message || '操作失败'), 'danger');
                }
            } catch (e) {
                showAlert('<i class="fas fa-exclamation-circle mr-1"></i> 服务器响应格式错误', 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('<i class="fas fa-exclamation-circle mr-1"></i> 服务器错误，请稍后重试', 'danger');
        }
    });
}

// 删除用户函数
function deleteUser(element) {
    const userId = $(element).data('user-id');
    const username = $(element).data('username');

    if (!confirm('确定要删除用户 ' + username + ' 吗？此操作不可恢复！')) {
        return;
    }

    $.ajax({
        url: 'users.php',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            delete_user: true,
            user_id: userId
        }),
        success: function(response) {
            try {
                const result = typeof response === 'string' ? JSON.parse(response) : response;

                if (result.status === 'success') {
                    showAlert('<i class="fas fa-check-circle mr-1"></i> ' + (result.message || '用户删除成功'), 'success');

                    // 延迟刷新页面，让用户看到成功消息
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    showAlert('<i class="fas fa-exclamation-circle mr-1"></i> ' + (result.message || '操作失败'), 'danger');
                }
            } catch (e) {
                showAlert('<i class="fas fa-exclamation-circle mr-1"></i> 服务器响应格式错误', 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('<i class="fas fa-exclamation-circle mr-1"></i> 服务器错误，请稍后重试', 'danger');
        }
    });
}
</script>

<!-- 解锁IP的隐藏表单 -->
<form id="unlockIpForm" method="post" style="display: none;">
    <input type="hidden" name="action" value="unlock_ip">
    <input type="hidden" name="ip_address" id="unlockIpAddress">
</form>

<!-- 清除所有IP记录的隐藏表单 -->
<form id="clearAllIpForm" method="post" style="display: none;">
    <input type="hidden" name="action" value="clear_all_ips">
</form>

</body>
</html>